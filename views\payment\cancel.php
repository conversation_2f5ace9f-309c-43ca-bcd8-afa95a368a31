<?php
/**
 * Cancelación de transacción
 * Esta página redirige al método correspondiente en la clase Payment
 */

// Definir la ruta base del proyecto
define('ROOT_PATH', dirname(dirname(__DIR__)));

// Cargar la configuración
require_once ROOT_PATH . '/config/config.php';

// Iniciar sesión
session_start();

// Inicializar la conexión a la base de datos
$db = Database::getInstance()->getConnection();

// Crear instancia de la clase Auth para verificar acceso
$auth = new Auth($db);

// Verificar si el usuario está autenticado
if (!$auth->isLoggedIn()) {
    // Redirigir al login
    header('Location: /login.php');
    exit;
}

// Obtener los parámetros de la transacción
$preferenceId = isset($_GET['preference_id']) ? $_GET['preference_id'] : null;

// Crear instancia de la clase Payment
$payment = new Payment($db);

// Mostrar la página de cancelación
$payment->showCancelPage($preferenceId);