<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AnimPlanetas - Sistema de Pestañas Elegante</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        'primary': '#1D5D20',
                        'secondary': '#437845',
                        'accent': '#68936A',
                        'light-green': '#8EAE90',
                        'lighter-green': '#B4C9B5',
                        'lightest-green': '#D9E4DA',
                        'white': '#FFFFFF'
                    },
                    screens: {
                        'compact': '480px',
                        'tablet': '768px',
                        'laptop': '1024px',
                        'desktop': '1280px',
                        'qhd': '1600px',
                        'uhd': '2560px'
                    }
                }
            }
        }
    </script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.0/css/all.min.css" rel="stylesheet">
    <style>
        /* Custom Animations */
        @keyframes slideInFromLeft {
            from { transform: translateX(-100%); opacity: 0; }
            to { transform: translateX(0); opacity: 1; }
        }

        @keyframes fadeInUp {
            from { transform: translateY(30px); opacity: 0; }
            to { transform: translateY(0); opacity: 1; }
        }

        @keyframes pulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.05); }
        }

        .animate-slide-in { animation: slideInFromLeft 0.5s ease-out; }
        .animate-fade-in-up { animation: fadeInUp 0.6s ease-out; }
        .animate-pulse-hover:hover { animation: pulse 0.3s ease-in-out; }

        /* Tab System Styles */
        .tab-content { display: none; }
        .tab-content.active { display: block; }

        .tab-button.active {
            background: linear-gradient(135deg, #1D5D20, #437845);
            color: white;
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(29, 93, 32, 0.3);
        }

        /* Dropdown Styles */
        .dropdown-menu {
            max-height: 0;
            overflow: hidden;
            transition: max-height 0.3s ease-in-out;
        }

        .dropdown-menu.active {
            max-height: 500px;
        }

        /* Video Container */
        .video-container {
            position: relative;
            width: 100%;
            height: 0;
            padding-bottom: 56.25%; /* 16:9 aspect ratio */
        }

        .video-container iframe {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            border-radius: 12px;
        }

        /* Gradient Backgrounds */
        .gradient-primary { background: linear-gradient(135deg, #1D5D20, #437845); }
        .gradient-secondary { background: linear-gradient(135deg, #437845, #68936A); }
        .gradient-accent { background: linear-gradient(135deg, #68936A, #8EAE90); }

        /* Responsive Grid */
        .responsive-grid {
            display: grid;
            gap: 1.5rem;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
        }

        @media (min-width: 768px) {
            .responsive-grid { grid-template-columns: repeat(2, 1fr); }
        }

        @media (min-width: 1024px) {
            .responsive-grid { grid-template-columns: repeat(3, 1fr); }
        }

        @media (min-width: 1600px) {
            .responsive-grid { grid-template-columns: repeat(4, 1fr); }
        }

        /* Elegant Card Hover Effects */
        .elegant-card {
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }

        .elegant-card:hover {
            transform: translateY(-8px);
            box-shadow: 0 20px 40px rgba(29, 93, 32, 0.15);
        }

        /* Navigation Enhancements */
        .nav-item {
            position: relative;
            overflow: hidden;
        }

        .nav-item::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.1), transparent);
            transition: left 0.5s;
        }

        .nav-item:hover::before {
            left: 100%;
        }
    </style>
</head>
<body class="bg-lightest-green min-h-screen">
    <!-- Header Navigation -->
    <header class="bg-white shadow-lg sticky top-0 z-50">
        <div class="container mx-auto px-4 py-4">
            <div class="flex items-center justify-between">
                <!-- Logo Section -->
                <div class="flex items-center space-x-4">
                    <div class="flex items-center space-x-3">
                        <div class="w-12 h-12 gradient-primary rounded-full flex items-center justify-center animate-pulse-hover">
                            <i class="fas fa-paw text-white text-xl"></i>
                        </div>
                        <h1 class="text-primary text-2xl font-bold">AnimPlanetas</h1>
                    </div>
                </div>

                <!-- Desktop Navigation -->
                <nav class="hidden laptop:flex items-center space-x-6">
                    <div class="flex items-center space-x-4">
                        <!-- Navigation Items -->
                        <div class="relative group">
                            <button class="nav-item px-4 py-2 text-primary hover:text-secondary transition-colors font-medium">
                                Inicio <i class="fas fa-chevron-down ml-1 text-sm"></i>
                            </button>
                            <div class="absolute top-full left-0 mt-2 w-64 bg-white rounded-lg shadow-xl opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-300 z-50">
                                <div class="p-4">
                                    <div class="video-container mb-3">
                                        <iframe src="https://www.youtube.com/embed/dQw4w9WgXcQ" title="Video Inicio"></iframe>
                                    </div>
                                    <p class="text-sm text-gray-600">Video de señas - Inicio</p>
                                </div>
                            </div>
                        </div>

                        <div class="relative group">
                            <button class="nav-item px-4 py-2 text-primary hover:text-secondary transition-colors font-medium">
                                AnimaleSeñas <i class="fas fa-chevron-down ml-1 text-sm"></i>
                            </button>
                            <div class="absolute top-full left-0 mt-2 w-64 bg-white rounded-lg shadow-xl opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-300 z-50">
                                <div class="p-4">
                                    <div class="video-container mb-3">
                                        <iframe src="https://www.youtube.com/embed/dQw4w9WgXcQ" title="Video AnimaleSeñas"></iframe>
                                    </div>
                                    <p class="text-sm text-gray-600">Video de señas - Animales</p>
                                </div>
                            </div>
                        </div>

                        <div class="relative group">
                            <button class="nav-item px-4 py-2 text-primary hover:text-secondary transition-colors font-medium">
                                PlanetaSeñas <i class="fas fa-chevron-down ml-1 text-sm"></i>
                            </button>
                            <div class="absolute top-full left-0 mt-2 w-64 bg-white rounded-lg shadow-xl opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-300 z-50">
                                <div class="p-4">
                                    <div class="video-container mb-3">
                                        <iframe src="https://www.youtube.com/embed/dQw4w9WgXcQ" title="Video PlanetaSeñas"></iframe>
                                    </div>
                                    <p class="text-sm text-gray-600">Video de señas - Planetas</p>
                                </div>
                            </div>
                        </div>

                        <div class="relative group">
                            <button class="nav-item px-4 py-2 text-primary hover:text-secondary transition-colors font-medium">
                                DiccionarioSeñas <i class="fas fa-chevron-down ml-1 text-sm"></i>
                            </button>
                            <div class="absolute top-full left-0 mt-2 w-64 bg-white rounded-lg shadow-xl opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-300 z-50">
                                <div class="p-4">
                                    <div class="video-container mb-3">
                                        <iframe src="https://www.youtube.com/embed/dQw4w9WgXcQ" title="Video DiccionarioSeñas"></iframe>
                                    </div>
                                    <p class="text-sm text-gray-600">Video de señas - Diccionario</p>
                                </div>
                            </div>
                        </div>

                        <div class="relative group">
                            <button class="nav-item px-4 py-2 text-primary hover:text-secondary transition-colors font-medium">
                                JuegoSeñas <i class="fas fa-chevron-down ml-1 text-sm"></i>
                            </button>
                            <div class="absolute top-full left-0 mt-2 w-64 bg-white rounded-lg shadow-xl opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-300 z-50">
                                <div class="p-4">
                                    <div class="video-container mb-3">
                                        <iframe src="https://www.youtube.com/embed/dQw4w9WgXcQ" title="Video JuegoSeñas"></iframe>
                                    </div>
                                    <p class="text-sm text-gray-600">Video de señas - Juegos</p>
                                </div>
                            </div>
                        </div>

                        <div class="relative group">
                            <button class="nav-item px-4 py-2 text-primary hover:text-secondary transition-colors font-medium">
                                VideoSeñas <i class="fas fa-chevron-down ml-1 text-sm"></i>
                            </button>
                            <div class="absolute top-full left-0 mt-2 w-64 bg-white rounded-lg shadow-xl opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-300 z-50">
                                <div class="p-4">
                                    <div class="video-container mb-3">
                                        <iframe src="https://www.youtube.com/embed/dQw4w9WgXcQ" title="Video VideoSeñas"></iframe>
                                    </div>
                                    <p class="text-sm text-gray-600">Video de señas - Videos</p>
                                </div>
                            </div>
                        </div>

                        <div class="relative group">
                            <button class="nav-item px-4 py-2 text-primary hover:text-secondary transition-colors font-medium">
                                ReflexiónSeñas <i class="fas fa-chevron-down ml-1 text-sm"></i>
                            </button>
                            <div class="absolute top-full left-0 mt-2 w-64 bg-white rounded-lg shadow-xl opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-300 z-50">
                                <div class="p-4">
                                    <div class="video-container mb-3">
                                        <iframe src="https://www.youtube.com/embed/dQw4w9WgXcQ" title="Video ReflexiónSeñas"></iframe>
                                    </div>
                                    <p class="text-sm text-gray-600">Video de señas - Reflexión</p>
                                </div>
                            </div>
                        </div>

                        <div class="relative group">
                            <button class="nav-item px-4 py-2 text-primary hover:text-secondary transition-colors font-medium">
                                TiendaSeñas <i class="fas fa-chevron-down ml-1 text-sm"></i>
                            </button>
                            <div class="absolute top-full left-0 mt-2 w-64 bg-white rounded-lg shadow-xl opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-300 z-50">
                                <div class="p-4">
                                    <div class="video-container mb-3">
                                        <iframe src="https://www.youtube.com/embed/dQw4w9WgXcQ" title="Video TiendaSeñas"></iframe>
                                    </div>
                                    <p class="text-sm text-gray-600">Video de señas - Tienda</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </nav>

                <!-- Right Side Icons -->
                <div class="flex items-center space-x-4">
                    <!-- Cart Icon with Dropdown -->
                    <div class="relative group">
                        <button class="p-3 text-primary hover:bg-lightest-green rounded-lg transition-all animate-pulse-hover">
                            <i class="fas fa-shopping-cart text-xl"></i>
                            <span class="absolute -top-2 -right-2 bg-secondary text-white text-xs rounded-full w-5 h-5 flex items-center justify-center">3</span>
                        </button>
                        <div class="absolute top-full right-0 mt-2 w-80 bg-white rounded-lg shadow-xl opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-300">
                            <div class="p-4">
                                <div class="video-container mb-3">
                                    <iframe src="https://www.youtube.com/embed/dQw4w9WgXcQ" title="Video Carrito"></iframe>
                                </div>
                                <div class="space-y-2">
                                    <div class="flex justify-between items-center">
                                        <span class="text-sm">Curso AnimaleSeñas</span>
                                        <span class="text-sm font-bold">$29.99</span>
                                    </div>
                                    <div class="flex justify-between items-center">
                                        <span class="text-sm">Diccionario Premium</span>
                                        <span class="text-sm font-bold">$19.99</span>
                                    </div>
                                </div>
                                <button class="w-full mt-3 bg-primary text-white py-2 rounded-lg hover:bg-secondary transition-colors">
                                    Ver Carrito
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- Profile Icon with Dropdown -->
                    <div class="relative group">
                        <button class="p-3 text-primary hover:bg-lightest-green rounded-lg transition-all animate-pulse-hover">
                            <i class="fas fa-user text-xl"></i>
                        </button>
                        <div class="absolute top-full right-0 mt-2 w-64 bg-white rounded-lg shadow-xl opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-300">
                            <div class="p-4">
                                <div class="video-container mb-3">
                                    <iframe src="https://www.youtube.com/embed/dQw4w9WgXcQ" title="Video Perfil"></iframe>
                                </div>
                                <div class="space-y-2">
                                    <a href="#" class="block px-3 py-2 text-sm hover:bg-lightest-green rounded">Mi Perfil</a>
                                    <a href="#" class="block px-3 py-2 text-sm hover:bg-lightest-green rounded">Mis Cursos</a>
                                    <a href="#" class="block px-3 py-2 text-sm hover:bg-lightest-green rounded">Configuración</a>
                                    <hr class="my-2">
                                    <a href="#" class="block px-3 py-2 text-sm text-red-600 hover:bg-red-50 rounded">Cerrar Sesión</a>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Mobile Menu Button -->
                    <button id="mobile-menu-btn" class="laptop:hidden p-2 text-primary hover:bg-lightest-green rounded-lg">
                        <i class="fas fa-bars text-xl"></i>
                    </button>
                </div>
            </div>

            <!-- Tab Navigation System -->
            <div class="border-t border-lightest-green mt-4 pt-4">
                <div class="flex flex-wrap gap-2 justify-center">
                    <button class="tab-button active px-4 py-2 rounded-lg font-medium transition-all duration-300 bg-lightest-green text-primary hover:bg-primary hover:text-white text-sm" data-tab="inicio">
                        <i class="fas fa-home mr-1"></i>Inicio
                    </button>
                    <button class="tab-button px-4 py-2 rounded-lg font-medium transition-all duration-300 bg-lightest-green text-primary hover:bg-primary hover:text-white text-sm" data-tab="animales">
                        <i class="fas fa-dog mr-1"></i>AnimaleSeñas
                    </button>
                    <button class="tab-button px-4 py-2 rounded-lg font-medium transition-all duration-300 bg-lightest-green text-primary hover:bg-primary hover:text-white text-sm" data-tab="planetas">
                        <i class="fas fa-globe mr-1"></i>PlanetaSeñas
                    </button>
                    <button class="tab-button px-4 py-2 rounded-lg font-medium transition-all duration-300 bg-lightest-green text-primary hover:bg-primary hover:text-white text-sm" data-tab="diccionario">
                        <i class="fas fa-book mr-1"></i>DiccionarioSeñas
                    </button>
                    <button class="tab-button px-4 py-2 rounded-lg font-medium transition-all duration-300 bg-lightest-green text-primary hover:bg-primary hover:text-white text-sm" data-tab="juegos">
                        <i class="fas fa-gamepad mr-1"></i>JuegoSeñas
                    </button>
                    <button class="tab-button px-4 py-2 rounded-lg font-medium transition-all duration-300 bg-lightest-green text-primary hover:bg-primary hover:text-white text-sm" data-tab="videos">
                        <i class="fas fa-play-circle mr-1"></i>VideoSeñas
                    </button>
                    <button class="tab-button px-4 py-2 rounded-lg font-medium transition-all duration-300 bg-lightest-green text-primary hover:bg-primary hover:text-white text-sm" data-tab="reflexion">
                        <i class="fas fa-lightbulb mr-1"></i>ReflexiónSeñas
                    </button>
                    <button class="tab-button px-4 py-2 rounded-lg font-medium transition-all duration-300 bg-lightest-green text-primary hover:bg-primary hover:text-white text-sm" data-tab="tienda">
                        <i class="fas fa-shopping-cart mr-1"></i>TiendaSeñas
                    </button>
                </div>
            </div>
        </div>
    </header>

    <!-- Mobile Navigation Menu -->
    <div id="mobile-menu" class="laptop:hidden fixed inset-0 bg-black bg-opacity-50 z-50 hidden">
        <div class="bg-white w-80 h-full overflow-y-auto">
            <div class="p-4 border-b">
                <div class="flex items-center justify-between">
                    <h2 class="text-xl font-bold text-primary">Menú</h2>
                    <button id="close-mobile-menu" class="p-2 text-primary hover:bg-lightest-green rounded">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
            </div>
            <nav class="p-4 space-y-2">
                <!-- Mobile Navigation Items -->
                <div class="mobile-nav-item">
                    <button class="w-full flex items-center justify-between p-3 text-primary hover:bg-lightest-green rounded-lg" data-target="inicio">
                        <span><i class="fas fa-home mr-3"></i>Inicio</span>
                        <i class="fas fa-chevron-down"></i>
                    </button>
                    <div class="dropdown-menu pl-6">
                        <div class="p-3">
                            <div class="video-container mb-2">
                                <iframe src="https://www.youtube.com/embed/dQw4w9WgXcQ" title="Video Inicio"></iframe>
                            </div>
                            <p class="text-sm text-gray-600">Video de señas - Inicio</p>
                        </div>
                    </div>
                </div>

                <div class="mobile-nav-item">
                    <button class="w-full flex items-center justify-between p-3 text-primary hover:bg-lightest-green rounded-lg" data-target="animales">
                        <span><i class="fas fa-dog mr-3"></i>AnimaleSeñas</span>
                        <i class="fas fa-chevron-down"></i>
                    </button>
                    <div class="dropdown-menu pl-6">
                        <div class="p-3">
                            <div class="video-container mb-2">
                                <iframe src="https://www.youtube.com/embed/dQw4w9WgXcQ" title="Video AnimaleSeñas"></iframe>
                            </div>
                            <p class="text-sm text-gray-600">Video de señas - Animales</p>
                        </div>
                    </div>
                </div>

                <div class="mobile-nav-item">
                    <button class="w-full flex items-center justify-between p-3 text-primary hover:bg-lightest-green rounded-lg" data-target="planetas">
                        <span><i class="fas fa-globe mr-3"></i>PlanetaSeñas</span>
                        <i class="fas fa-chevron-down"></i>
                    </button>
                    <div class="dropdown-menu pl-6">
                        <div class="p-3">
                            <div class="video-container mb-2">
                                <iframe src="https://www.youtube.com/embed/dQw4w9WgXcQ" title="Video PlanetaSeñas"></iframe>
                            </div>
                            <p class="text-sm text-gray-600">Video de señas - Planetas</p>
                        </div>
                    </div>
                </div>

                <div class="mobile-nav-item">
                    <button class="w-full flex items-center justify-between p-3 text-primary hover:bg-lightest-green rounded-lg" data-target="diccionario">
                        <span><i class="fas fa-book mr-3"></i>DiccionarioSeñas</span>
                        <i class="fas fa-chevron-down"></i>
                    </button>
                    <div class="dropdown-menu pl-6">
                        <div class="p-3">
                            <div class="video-container mb-2">
                                <iframe src="https://www.youtube.com/embed/dQw4w9WgXcQ" title="Video DiccionarioSeñas"></iframe>
                            </div>
                            <p class="text-sm text-gray-600">Video de señas - Diccionario</p>
                        </div>
                    </div>
                </div>

                <div class="mobile-nav-item">
                    <button class="w-full flex items-center justify-between p-3 text-primary hover:bg-lightest-green rounded-lg" data-target="juegos">
                        <span><i class="fas fa-gamepad mr-3"></i>JuegoSeñas</span>
                        <i class="fas fa-chevron-down"></i>
                    </button>
                    <div class="dropdown-menu pl-6">
                        <div class="p-3">
                            <div class="video-container mb-2">
                                <iframe src="https://www.youtube.com/embed/dQw4w9WgXcQ" title="Video JuegoSeñas"></iframe>
                            </div>
                            <p class="text-sm text-gray-600">Video de señas - Juegos</p>
                        </div>
                    </div>
                </div>

                <div class="mobile-nav-item">
                    <button class="w-full flex items-center justify-between p-3 text-primary hover:bg-lightest-green rounded-lg" data-target="videos">
                        <span><i class="fas fa-play-circle mr-3"></i>VideoSeñas</span>
                        <i class="fas fa-chevron-down"></i>
                    </button>
                    <div class="dropdown-menu pl-6">
                        <div class="p-3">
                            <div class="video-container mb-2">
                                <iframe src="https://www.youtube.com/embed/dQw4w9WgXcQ" title="Video VideoSeñas"></iframe>
                            </div>
                            <p class="text-sm text-gray-600">Video de señas - Videos</p>
                        </div>
                    </div>
                </div>

                <div class="mobile-nav-item">
                    <button class="w-full flex items-center justify-between p-3 text-primary hover:bg-lightest-green rounded-lg" data-target="reflexion">
                        <span><i class="fas fa-lightbulb mr-3"></i>ReflexiónSeñas</span>
                        <i class="fas fa-chevron-down"></i>
                    </button>
                    <div class="dropdown-menu pl-6">
                        <div class="p-3">
                            <div class="video-container mb-2">
                                <iframe src="https://www.youtube.com/embed/dQw4w9WgXcQ" title="Video ReflexiónSeñas"></iframe>
                            </div>
                            <p class="text-sm text-gray-600">Video de señas - Reflexión</p>
                        </div>
                    </div>
                </div>

                <div class="mobile-nav-item">
                    <button class="w-full flex items-center justify-between p-3 text-primary hover:bg-lightest-green rounded-lg" data-target="tienda">
                        <span><i class="fas fa-shopping-cart mr-3"></i>TiendaSeñas</span>
                        <i class="fas fa-chevron-down"></i>
                    </button>
                    <div class="dropdown-menu pl-6">
                        <div class="p-3">
                            <div class="video-container mb-2">
                                <iframe src="https://www.youtube.com/embed/dQw4w9WgXcQ" title="Video TiendaSeñas"></iframe>
                            </div>
                            <p class="text-sm text-gray-600">Video de señas - Tienda</p>
                        </div>
                    </div>
                </div>
            </nav>
        </div>
    </div>

   
    <!-- Main Tab System Content -->
    <main class="container mx-auto px-4 py-8">
        <!-- Tab Content -->
        <div class="tab-content-container">
            <!-- Inicio Tab -->
            <div id="inicio" class="tab-content active">
                <div class="animate-fade-in-up">
                    <!-- Hero Section with Carousel -->
                    <section class="gradient-primary rounded-2xl p-8 text-white mb-8">
                        <div class="text-center mb-8">
                            <h2 class="text-4xl compact:text-5xl font-bold mb-4">Bienvenido a AnimPlanetas</h2>
                            <p class="text-xl text-lighter-green">Tu aventura en el lenguaje de señas comienza aquí</p>
                        </div>

                        <!-- Featured Video -->
                        <div class="max-w-4xl mx-auto">
                            <div class="bg-white/10 backdrop-blur-sm rounded-xl p-6">
                                <h3 class="text-2xl font-bold mb-4 text-center">Video Destacado - Inicio</h3>
                                <div class="video-container">
                                    <iframe src="https://www.youtube.com/embed/dQw4w9WgXcQ" title="Video Principal Inicio" allowfullscreen></iframe>
                                </div>
                            </div>
                        </div>
                    </section>

                    <!-- Statistics Section -->
                    <section class="responsive-grid mb-8">
                        <div class="elegant-card bg-white p-6 rounded-xl shadow-lg text-center">
                            <div class="w-16 h-16 gradient-primary rounded-full flex items-center justify-center mx-auto mb-4">
                                <i class="fas fa-users text-2xl text-white"></i>
                            </div>
                            <h3 class="text-3xl font-bold text-primary mb-2">15,000+</h3>
                            <p class="text-gray-600">Estudiantes Activos</p>
                        </div>

                        <div class="elegant-card bg-white p-6 rounded-xl shadow-lg text-center">
                            <div class="w-16 h-16 gradient-secondary rounded-full flex items-center justify-center mx-auto mb-4">
                                <i class="fas fa-video text-2xl text-white"></i>
                            </div>
                            <h3 class="text-3xl font-bold text-secondary mb-2">800+</h3>
                            <p class="text-gray-600">Videos de Señas</p>
                        </div>

                        <div class="elegant-card bg-white p-6 rounded-xl shadow-lg text-center">
                            <div class="w-16 h-16 gradient-accent rounded-full flex items-center justify-center mx-auto mb-4">
                                <i class="fas fa-trophy text-2xl text-white"></i>
                            </div>
                            <h3 class="text-3xl font-bold text-accent mb-2">98%</h3>
                            <p class="text-gray-600">Tasa de Éxito</p>
                        </div>

                        <div class="elegant-card bg-white p-6 rounded-xl shadow-lg text-center">
                            <div class="w-16 h-16 bg-gradient-to-br from-light-green to-lighter-green rounded-full flex items-center justify-center mx-auto mb-4">
                                <i class="fas fa-star text-2xl text-white"></i>
                            </div>
                            <h3 class="text-3xl font-bold text-light-green mb-2">4.9/5</h3>
                            <p class="text-gray-600">Calificación</p>
                        </div>
                    </section>

                    <!-- CTA Section -->
                    <section class="bg-white rounded-2xl p-8 text-center shadow-lg">
                        <h2 class="text-3xl font-bold text-primary mb-4">¡Comienza tu Aventura Hoy!</h2>
                        <p class="text-xl text-gray-600 mb-6 max-w-2xl mx-auto">
                            Únete a miles de personas que ya están transformando sus vidas con el lenguaje de señas
                        </p>
                        <div class="flex flex-col compact:flex-row gap-4 justify-center">
                            <button class="gradient-primary text-white px-8 py-4 rounded-lg text-lg font-semibold transition-all transform hover:scale-105 animate-pulse-hover">
                                Empezar Gratis
                            </button>
                            <button class="border-2 border-primary text-primary hover:bg-primary hover:text-white px-8 py-4 rounded-lg text-lg font-semibold transition-all">
                                Ver Demo
                            </button>
                        </div>
                    </section>
                </div>
            </div>

            <!-- AnimaleSeñas Tab -->
            <div id="animales" class="tab-content">
                <div class="animate-fade-in-up">
                    <section class="gradient-secondary rounded-2xl p-8 text-white mb-8">
                        <div class="text-center mb-8">
                            <h2 class="text-4xl font-bold mb-4">AnimaleSeñas</h2>
                            <p class="text-xl text-lighter-green">Aprende las señas de tus animales favoritos</p>
                        </div>

                        <div class="max-w-4xl mx-auto">
                            <div class="bg-white/10 backdrop-blur-sm rounded-xl p-6">
                                <h3 class="text-2xl font-bold mb-4 text-center">Video Tutorial - AnimaleSeñas</h3>
                                <div class="video-container">
                                    <iframe src="https://www.youtube.com/embed/dQw4w9WgXcQ" title="Video AnimaleSeñas" allowfullscreen></iframe>
                                </div>
                            </div>
                        </div>
                    </section>

                    <div class="responsive-grid">
                        <div class="elegant-card bg-white rounded-xl p-6 shadow-lg">
                            <div class="w-20 h-20 gradient-secondary rounded-full flex items-center justify-center mx-auto mb-4">
                                <i class="fas fa-dog text-3xl text-white"></i>
                            </div>
                            <h3 class="text-xl font-bold text-primary mb-2">Animales Domésticos</h3>
                            <p class="text-gray-600 mb-4">Perros, gatos, pájaros y más</p>
                            <button class="w-full bg-secondary text-white py-2 rounded-lg hover:bg-primary transition-colors">
                                Explorar
                            </button>
                        </div>

                        <div class="elegant-card bg-white rounded-xl p-6 shadow-lg">
                            <div class="w-20 h-20 gradient-secondary rounded-full flex items-center justify-center mx-auto mb-4">
                                <i class="fas fa-horse text-3xl text-white"></i>
                            </div>
                            <h3 class="text-xl font-bold text-primary mb-2">Animales de Granja</h3>
                            <p class="text-gray-600 mb-4">Vacas, cerdos, gallinas y más</p>
                            <button class="w-full bg-secondary text-white py-2 rounded-lg hover:bg-primary transition-colors">
                                Explorar
                            </button>
                        </div>

                        <div class="elegant-card bg-white rounded-xl p-6 shadow-lg">
                            <div class="w-20 h-20 gradient-secondary rounded-full flex items-center justify-center mx-auto mb-4">
                                <i class="fas fa-fish text-3xl text-white"></i>
                            </div>
                            <h3 class="text-xl font-bold text-primary mb-2">Animales Marinos</h3>
                            <p class="text-gray-600 mb-4">Peces, delfines, ballenas y más</p>
                            <button class="w-full bg-secondary text-white py-2 rounded-lg hover:bg-primary transition-colors">
                                Explorar
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- PlanetaSeñas Tab -->
            <div id="planetas" class="tab-content">
                <div class="animate-fade-in-up">
                    <section class="gradient-accent rounded-2xl p-8 text-white mb-8">
                        <div class="text-center mb-8">
                            <h2 class="text-4xl font-bold mb-4">PlanetaSeñas</h2>
                            <p class="text-xl text-lighter-green">Explora el mundo natural a través de las señas</p>
                        </div>

                        <div class="max-w-4xl mx-auto">
                            <div class="bg-white/10 backdrop-blur-sm rounded-xl p-6">
                                <h3 class="text-2xl font-bold mb-4 text-center">Video Tutorial - PlanetaSeñas</h3>
                                <div class="video-container">
                                    <iframe src="https://www.youtube.com/embed/dQw4w9WgXcQ" title="Video PlanetaSeñas" allowfullscreen></iframe>
                                </div>
                            </div>
                        </div>
                    </section>

                    <div class="responsive-grid">
                        <div class="elegant-card bg-white rounded-xl p-6 shadow-lg">
                            <div class="w-20 h-20 gradient-accent rounded-full flex items-center justify-center mx-auto mb-4">
                                <i class="fas fa-leaf text-3xl text-white"></i>
                            </div>
                            <h3 class="text-xl font-bold text-primary mb-2">Plantas y Naturaleza</h3>
                            <p class="text-gray-600 mb-4">Árboles, flores, ecosistemas</p>
                            <button class="w-full bg-accent text-white py-2 rounded-lg hover:bg-primary transition-colors">
                                Explorar
                            </button>
                        </div>

                        <div class="elegant-card bg-white rounded-xl p-6 shadow-lg">
                            <div class="w-20 h-20 gradient-accent rounded-full flex items-center justify-center mx-auto mb-4">
                                <i class="fas fa-cloud-sun text-3xl text-white"></i>
                            </div>
                            <h3 class="text-xl font-bold text-primary mb-2">Clima y Tiempo</h3>
                            <p class="text-gray-600 mb-4">Sol, lluvia, viento, estaciones</p>
                            <button class="w-full bg-accent text-white py-2 rounded-lg hover:bg-primary transition-colors">
                                Explorar
                            </button>
                        </div>

                        <div class="elegant-card bg-white rounded-xl p-6 shadow-lg">
                            <div class="w-20 h-20 gradient-accent rounded-full flex items-center justify-center mx-auto mb-4">
                                <i class="fas fa-mountain text-3xl text-white"></i>
                            </div>
                            <h3 class="text-xl font-bold text-primary mb-2">Geografía</h3>
                            <p class="text-gray-600 mb-4">Montañas, ríos, océanos</p>
                            <button class="w-full bg-accent text-white py-2 rounded-lg hover:bg-primary transition-colors">
                                Explorar
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- DiccionarioSeñas Tab -->
            <div id="diccionario" class="tab-content">
                <div class="animate-fade-in-up">
                    <section class="bg-gradient-to-br from-primary to-accent rounded-2xl p-8 text-white mb-8">
                        <div class="text-center mb-8">
                            <h2 class="text-4xl font-bold mb-4">DiccionarioSeñas</h2>
                            <p class="text-xl text-lighter-green">Tu biblioteca completa de señas</p>
                        </div>

                        <div class="max-w-4xl mx-auto">
                            <div class="bg-white/10 backdrop-blur-sm rounded-xl p-6">
                                <h3 class="text-2xl font-bold mb-4 text-center">Video Tutorial - DiccionarioSeñas</h3>
                                <div class="video-container">
                                    <iframe src="https://www.youtube.com/embed/dQw4w9WgXcQ" title="Video DiccionarioSeñas" allowfullscreen></iframe>
                                </div>
                            </div>
                        </div>
                    </section>

                    <!-- Search Section -->
                    <div class="bg-white rounded-2xl p-6 shadow-lg mb-8">
                        <div class="max-w-2xl mx-auto">
                            <h3 class="text-2xl font-bold text-primary mb-4 text-center">Buscar Señas</h3>
                            <div class="flex gap-4">
                                <input type="text" placeholder="Buscar palabra o seña..." class="flex-1 px-4 py-3 border border-lighter-green rounded-lg focus:outline-none focus:ring-2 focus:ring-primary">
                                <button class="gradient-primary text-white px-6 py-3 rounded-lg hover:opacity-90 transition-opacity">
                                    <i class="fas fa-search"></i>
                                </button>
                            </div>
                        </div>
                    </div>

                    <div class="responsive-grid">
                        <div class="elegant-card bg-white rounded-xl p-6 shadow-lg">
                            <h3 class="text-xl font-bold text-primary mb-4">Categorías Populares</h3>
                            <div class="space-y-2">
                                <a href="#" class="block p-3 bg-lightest-green rounded-lg hover:bg-lighter-green transition-colors">
                                    <i class="fas fa-home mr-2 text-primary"></i>Familia
                                </a>
                                <a href="#" class="block p-3 bg-lightest-green rounded-lg hover:bg-lighter-green transition-colors">
                                    <i class="fas fa-utensils mr-2 text-primary"></i>Comida
                                </a>
                                <a href="#" class="block p-3 bg-lightest-green rounded-lg hover:bg-lighter-green transition-colors">
                                    <i class="fas fa-heart mr-2 text-primary"></i>Emociones
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- JuegoSeñas Tab -->
            <div id="juegos" class="tab-content">
                <div class="animate-fade-in-up">
                    <section class="bg-gradient-to-br from-secondary to-light-green rounded-2xl p-8 text-white mb-8">
                        <div class="text-center mb-8">
                            <h2 class="text-4xl font-bold mb-4">JuegoSeñas</h2>
                            <p class="text-xl text-lighter-green">Aprende jugando de manera divertida</p>
                        </div>

                        <div class="max-w-4xl mx-auto">
                            <div class="bg-white/10 backdrop-blur-sm rounded-xl p-6">
                                <h3 class="text-2xl font-bold mb-4 text-center">Video Tutorial - JuegoSeñas</h3>
                                <div class="video-container">
                                    <iframe src="https://www.youtube.com/embed/dQw4w9WgXcQ" title="Video JuegoSeñas" allowfullscreen></iframe>
                                </div>
                            </div>
                        </div>
                    </section>

                    <div class="responsive-grid">
                        <div class="elegant-card bg-white rounded-xl p-6 shadow-lg">
                            <div class="w-20 h-20 bg-gradient-to-br from-secondary to-light-green rounded-full flex items-center justify-center mx-auto mb-4">
                                <i class="fas fa-puzzle-piece text-3xl text-white"></i>
                            </div>
                            <h3 class="text-xl font-bold text-primary mb-2">Memoria de Señas</h3>
                            <p class="text-gray-600 mb-4">Juego de memoria con señas</p>
                            <button class="w-full bg-secondary text-white py-2 rounded-lg hover:bg-primary transition-colors">
                                Jugar
                            </button>
                        </div>

                        <div class="elegant-card bg-white rounded-xl p-6 shadow-lg">
                            <div class="w-20 h-20 bg-gradient-to-br from-secondary to-light-green rounded-full flex items-center justify-center mx-auto mb-4">
                                <i class="fas fa-crosshairs text-3xl text-white"></i>
                            </div>
                            <h3 class="text-xl font-bold text-primary mb-2">Adivina la Seña</h3>
                            <p class="text-gray-600 mb-4">Identifica la seña correcta</p>
                            <button class="w-full bg-secondary text-white py-2 rounded-lg hover:bg-primary transition-colors">
                                Jugar
                            </button>
                        </div>

                        <div class="elegant-card bg-white rounded-xl p-6 shadow-lg">
                            <div class="w-20 h-20 bg-gradient-to-br from-secondary to-light-green rounded-full flex items-center justify-center mx-auto mb-4">
                                <i class="fas fa-clock text-3xl text-white"></i>
                            </div>
                            <h3 class="text-xl font-bold text-primary mb-2">Desafío Rápido</h3>
                            <p class="text-gray-600 mb-4">Señas contra el tiempo</p>
                            <button class="w-full bg-secondary text-white py-2 rounded-lg hover:bg-primary transition-colors">
                                Jugar
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- VideoSeñas Tab -->
            <div id="videos" class="tab-content">
                <div class="animate-fade-in-up">
                    <section class="bg-gradient-to-br from-accent to-lighter-green rounded-2xl p-8 text-white mb-8">
                        <div class="text-center mb-8">
                            <h2 class="text-4xl font-bold mb-4">VideoSeñas</h2>
                            <p class="text-xl text-lighter-green">Biblioteca completa de videos educativos</p>
                        </div>

                        <div class="max-w-4xl mx-auto">
                            <div class="bg-white/10 backdrop-blur-sm rounded-xl p-6">
                                <h3 class="text-2xl font-bold mb-4 text-center">Video Tutorial - VideoSeñas</h3>
                                <div class="video-container">
                                    <iframe src="https://www.youtube.com/embed/dQw4w9WgXcQ" title="Video VideoSeñas" allowfullscreen></iframe>
                                </div>
                            </div>
                        </div>
                    </section>

                    <div class="responsive-grid">
                        <div class="elegant-card bg-white rounded-xl p-6 shadow-lg">
                            <div class="aspect-video bg-gradient-to-br from-accent to-lighter-green rounded-lg mb-4 flex items-center justify-center">
                                <i class="fas fa-play text-4xl text-white"></i>
                            </div>
                            <h3 class="text-lg font-bold text-primary mb-2">Curso Básico</h3>
                            <p class="text-gray-600 text-sm mb-3">Fundamentos del lenguaje de señas</p>
                            <div class="flex items-center justify-between">
                                <span class="text-xs text-gray-500">15 videos</span>
                                <button class="bg-accent text-white px-4 py-1 rounded text-sm hover:bg-primary transition-colors">
                                    Ver
                                </button>
                            </div>
                        </div>

                        <div class="elegant-card bg-white rounded-xl p-6 shadow-lg">
                            <div class="aspect-video bg-gradient-to-br from-accent to-lighter-green rounded-lg mb-4 flex items-center justify-center">
                                <i class="fas fa-play text-4xl text-white"></i>
                            </div>
                            <h3 class="text-lg font-bold text-primary mb-2">Nivel Intermedio</h3>
                            <p class="text-gray-600 text-sm mb-3">Conversaciones y expresiones</p>
                            <div class="flex items-center justify-between">
                                <span class="text-xs text-gray-500">20 videos</span>
                                <button class="bg-accent text-white px-4 py-1 rounded text-sm hover:bg-primary transition-colors">
                                    Ver
                                </button>
                            </div>
                        </div>

                        <div class="elegant-card bg-white rounded-xl p-6 shadow-lg">
                            <div class="aspect-video bg-gradient-to-br from-accent to-lighter-green rounded-lg mb-4 flex items-center justify-center">
                                <i class="fas fa-play text-4xl text-white"></i>
                            </div>
                            <h3 class="text-lg font-bold text-primary mb-2">Nivel Avanzado</h3>
                            <p class="text-gray-600 text-sm mb-3">Técnicas profesionales</p>
                            <div class="flex items-center justify-between">
                                <span class="text-xs text-gray-500">25 videos</span>
                                <button class="bg-accent text-white px-4 py-1 rounded text-sm hover:bg-primary transition-colors">
                                    Ver
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- ReflexiónSeñas Tab -->
            <div id="reflexion" class="tab-content">
                <div class="animate-fade-in-up">
                    <section class="bg-gradient-to-br from-light-green to-lighter-green rounded-2xl p-8 text-white mb-8">
                        <div class="text-center mb-8">
                            <h2 class="text-4xl font-bold mb-4">ReflexiónSeñas</h2>
                            <p class="text-xl">Reflexiones sobre inclusión y comunicación</p>
                        </div>

                        <div class="max-w-4xl mx-auto">
                            <div class="bg-white/10 backdrop-blur-sm rounded-xl p-6">
                                <h3 class="text-2xl font-bold mb-4 text-center">Video Tutorial - ReflexiónSeñas</h3>
                                <div class="video-container">
                                    <iframe src="https://www.youtube.com/embed/dQw4w9WgXcQ" title="Video ReflexiónSeñas" allowfullscreen></iframe>
                                </div>
                            </div>
                        </div>
                    </section>

                    <div class="responsive-grid">
                        <div class="elegant-card bg-white rounded-xl p-6 shadow-lg">
                            <div class="w-20 h-20 bg-gradient-to-br from-light-green to-lighter-green rounded-full flex items-center justify-center mx-auto mb-4">
                                <i class="fas fa-heart text-3xl text-white"></i>
                            </div>
                            <h3 class="text-xl font-bold text-primary mb-2">Inclusión Social</h3>
                            <p class="text-gray-600 mb-4">La importancia de la comunicación inclusiva</p>
                            <button class="w-full bg-light-green text-white py-2 rounded-lg hover:bg-primary transition-colors">
                                Leer Más
                            </button>
                        </div>

                        <div class="elegant-card bg-white rounded-xl p-6 shadow-lg">
                            <div class="w-20 h-20 bg-gradient-to-br from-light-green to-lighter-green rounded-full flex items-center justify-center mx-auto mb-4">
                                <i class="fas fa-hands text-3xl text-white"></i>
                            </div>
                            <h3 class="text-xl font-bold text-primary mb-2">Cultura Sorda</h3>
                            <p class="text-gray-600 mb-4">Entendiendo la comunidad sorda</p>
                            <button class="w-full bg-light-green text-white py-2 rounded-lg hover:bg-primary transition-colors">
                                Leer Más
                            </button>
                        </div>

                        <div class="elegant-card bg-white rounded-xl p-6 shadow-lg">
                            <div class="w-20 h-20 bg-gradient-to-br from-light-green to-lighter-green rounded-full flex items-center justify-center mx-auto mb-4">
                                <i class="fas fa-globe text-3xl text-white"></i>
                            </div>
                            <h3 class="text-xl font-bold text-primary mb-2">Impacto Global</h3>
                            <p class="text-gray-600 mb-4">Las señas alrededor del mundo</p>
                            <button class="w-full bg-light-green text-white py-2 rounded-lg hover:bg-primary transition-colors">
                                Leer Más
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- TiendaSeñas Tab -->
            <div id="tienda" class="tab-content">
                <div class="animate-fade-in-up">
                    <section class="gradient-primary rounded-2xl p-8 text-white mb-8">
                        <div class="text-center mb-8">
                            <h2 class="text-4xl font-bold mb-4">TiendaSeñas</h2>
                            <p class="text-xl text-lighter-green">Cursos, materiales y recursos premium</p>
                        </div>

                        <div class="max-w-4xl mx-auto">
                            <div class="bg-white/10 backdrop-blur-sm rounded-xl p-6">
                                <h3 class="text-2xl font-bold mb-4 text-center">Video Tutorial - TiendaSeñas</h3>
                                <div class="video-container">
                                    <iframe src="https://www.youtube.com/embed/dQw4w9WgXcQ" title="Video TiendaSeñas" allowfullscreen></iframe>
                                </div>
                            </div>
                        </div>
                    </section>

                    <!-- Featured Products -->
                    <div class="bg-white rounded-2xl p-6 shadow-lg mb-8">
                        <h3 class="text-2xl font-bold text-primary mb-6 text-center">Productos Destacados</h3>
                        <div class="responsive-grid">
                            <div class="elegant-card border border-lighter-green rounded-xl p-6">
                                <div class="aspect-square bg-gradient-to-br from-primary to-secondary rounded-lg mb-4 flex items-center justify-center">
                                    <i class="fas fa-graduation-cap text-4xl text-white"></i>
                                </div>
                                <h4 class="text-lg font-bold text-primary mb-2">Curso Completo</h4>
                                <p class="text-gray-600 text-sm mb-3">Aprende desde cero hasta nivel avanzado</p>
                                <div class="flex items-center justify-between mb-3">
                                    <span class="text-2xl font-bold text-primary">$49.99</span>
                                    <div class="flex text-yellow-400">
                                        <i class="fas fa-star"></i>
                                        <i class="fas fa-star"></i>
                                        <i class="fas fa-star"></i>
                                        <i class="fas fa-star"></i>
                                        <i class="fas fa-star"></i>
                                    </div>
                                </div>
                                <button class="w-full gradient-primary text-white py-2 rounded-lg hover:opacity-90 transition-opacity">
                                    <i class="fas fa-cart-plus mr-2"></i>Agregar al Carrito
                                </button>
                            </div>

                            <div class="elegant-card border border-lighter-green rounded-xl p-6">
                                <div class="aspect-square bg-gradient-to-br from-secondary to-accent rounded-lg mb-4 flex items-center justify-center">
                                    <i class="fas fa-book text-4xl text-white"></i>
                                </div>
                                <h4 class="text-lg font-bold text-primary mb-2">Diccionario Premium</h4>
                                <p class="text-gray-600 text-sm mb-3">Acceso completo a todas las señas</p>
                                <div class="flex items-center justify-between mb-3">
                                    <span class="text-2xl font-bold text-primary">$29.99</span>
                                    <div class="flex text-yellow-400">
                                        <i class="fas fa-star"></i>
                                        <i class="fas fa-star"></i>
                                        <i class="fas fa-star"></i>
                                        <i class="fas fa-star"></i>
                                        <i class="fas fa-star"></i>
                                    </div>
                                </div>
                                <button class="w-full gradient-secondary text-white py-2 rounded-lg hover:opacity-90 transition-opacity">
                                    <i class="fas fa-cart-plus mr-2"></i>Agregar al Carrito
                                </button>
                            </div>

                            <div class="elegant-card border border-lighter-green rounded-xl p-6">
                                <div class="aspect-square bg-gradient-to-br from-accent to-light-green rounded-lg mb-4 flex items-center justify-center">
                                    <i class="fas fa-gamepad text-4xl text-white"></i>
                                </div>
                                <h4 class="text-lg font-bold text-primary mb-2">Pack de Juegos</h4>
                                <p class="text-gray-600 text-sm mb-3">Colección completa de juegos educativos</p>
                                <div class="flex items-center justify-between mb-3">
                                    <span class="text-2xl font-bold text-primary">$19.99</span>
                                    <div class="flex text-yellow-400">
                                        <i class="fas fa-star"></i>
                                        <i class="fas fa-star"></i>
                                        <i class="fas fa-star"></i>
                                        <i class="fas fa-star"></i>
                                        <i class="far fa-star"></i>
                                    </div>
                                </div>
                                <button class="w-full gradient-accent text-white py-2 rounded-lg hover:opacity-90 transition-opacity">
                                    <i class="fas fa-cart-plus mr-2"></i>Agregar al Carrito
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- Shopping Cart Summary -->
                    <div class="bg-lightest-green rounded-2xl p-6 shadow-lg">
                        <h3 class="text-xl font-bold text-primary mb-4">
                            <i class="fas fa-shopping-cart mr-2"></i>Resumen del Carrito
                        </h3>
                        <div class="space-y-3">
                            <div class="flex justify-between items-center p-3 bg-white rounded-lg">
                                <span>Curso Completo</span>
                                <span class="font-bold">$49.99</span>
                            </div>
                            <div class="flex justify-between items-center p-3 bg-white rounded-lg">
                                <span>Diccionario Premium</span>
                                <span class="font-bold">$29.99</span>
                            </div>
                            <hr class="border-lighter-green">
                            <div class="flex justify-between items-center text-lg font-bold">
                                <span>Total:</span>
                                <span class="text-primary">$79.98</span>
                            </div>
                            <button class="w-full gradient-primary text-white py-3 rounded-lg text-lg font-semibold hover:opacity-90 transition-opacity">
                                <i class="fas fa-credit-card mr-2"></i>Proceder al Pago
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <!-- Team Section -->
    <section class="py-16 bg-white">
        <div class="container mx-auto px-4">
            <div class="text-center mb-12">
                <h2 class="text-4xl font-bold text-primary mb-4">Nuestro Equipo</h2>
                <p class="text-xl text-gray-600">Expertos dedicados a la educación inclusiva</p>
            </div>

            <div class="responsive-grid">
                <div class="elegant-card bg-lightest-green rounded-xl p-6 text-center shadow-lg">
                    <div class="w-24 h-24 gradient-primary rounded-full mx-auto mb-4 flex items-center justify-center">
                        <i class="fas fa-user text-3xl text-white"></i>
                    </div>
                    <h3 class="text-xl font-bold text-primary mb-2">María González</h3>
                    <p class="text-gray-600 mb-2">Especialista en Lenguaje de Señas</p>
                    <p class="text-sm text-gray-500">15 años de experiencia en educación inclusiva</p>
                </div>

                <div class="elegant-card bg-lightest-green rounded-xl p-6 text-center shadow-lg">
                    <div class="w-24 h-24 gradient-secondary rounded-full mx-auto mb-4 flex items-center justify-center">
                        <i class="fas fa-user text-3xl text-white"></i>
                    </div>
                    <h3 class="text-xl font-bold text-primary mb-2">Carlos Rodríguez</h3>
                    <p class="text-gray-600 mb-2">Desarrollador de Contenido</p>
                    <p class="text-sm text-gray-500">Experto en tecnología educativa</p>
                </div>

                <div class="elegant-card bg-lightest-green rounded-xl p-6 text-center shadow-lg">
                    <div class="w-24 h-24 gradient-accent rounded-full mx-auto mb-4 flex items-center justify-center">
                        <i class="fas fa-user text-3xl text-white"></i>
                    </div>
                    <h3 class="text-xl font-bold text-primary mb-2">Ana Martínez</h3>
                    <p class="text-gray-600 mb-2">Diseñadora UX/UI</p>
                    <p class="text-sm text-gray-500">Especialista en accesibilidad digital</p>
                </div>
            </div>
        </div>
    </section>

    <!-- Testimonials Section -->
    <section class="py-16 bg-lightest-green">
        <div class="container mx-auto px-4">
            <div class="text-center mb-12">
                <h2 class="text-4xl font-bold text-primary mb-4">Testimonios</h2>
                <p class="text-xl text-gray-600">Lo que dicen nuestros estudiantes</p>
            </div>

            <div class="responsive-grid">
                <div class="elegant-card bg-white p-6 rounded-xl shadow-lg">
                    <div class="flex items-center mb-4">
                        <div class="w-12 h-12 gradient-primary rounded-full flex items-center justify-center mr-4">
                            <i class="fas fa-user text-white"></i>
                        </div>
                        <div>
                            <h4 class="font-bold text-primary">Laura Pérez</h4>
                            <div class="flex text-yellow-400">
                                <i class="fas fa-star"></i>
                                <i class="fas fa-star"></i>
                                <i class="fas fa-star"></i>
                                <i class="fas fa-star"></i>
                                <i class="fas fa-star"></i>
                            </div>
                        </div>
                    </div>
                    <p class="text-gray-600">"AnimPlanetas ha transformado la manera en que mi familia se comunica. Los videos son increíbles y muy fáciles de seguir."</p>
                </div>

                <div class="elegant-card bg-white p-6 rounded-xl shadow-lg">
                    <div class="flex items-center mb-4">
                        <div class="w-12 h-12 gradient-secondary rounded-full flex items-center justify-center mr-4">
                            <i class="fas fa-user text-white"></i>
                        </div>
                        <div>
                            <h4 class="font-bold text-primary">Roberto Silva</h4>
                            <div class="flex text-yellow-400">
                                <i class="fas fa-star"></i>
                                <i class="fas fa-star"></i>
                                <i class="fas fa-star"></i>
                                <i class="fas fa-star"></i>
                                <i class="fas fa-star"></i>
                            </div>
                        </div>
                    </div>
                    <p class="text-gray-600">"Como profesor, recomiendo AnimPlanetas a todos mis colegas. Es una herramienta educativa excepcional."</p>
                </div>

                <div class="elegant-card bg-white p-6 rounded-xl shadow-lg">
                    <div class="flex items-center mb-4">
                        <div class="w-12 h-12 gradient-accent rounded-full flex items-center justify-center mr-4">
                            <i class="fas fa-user text-white"></i>
                        </div>
                        <div>
                            <h4 class="font-bold text-primary">Carmen López</h4>
                            <div class="flex text-yellow-400">
                                <i class="fas fa-star"></i>
                                <i class="fas fa-star"></i>
                                <i class="fas fa-star"></i>
                                <i class="fas fa-star"></i>
                                <i class="fas fa-star"></i>
                            </div>
                        </div>
                    </div>
                    <p class="text-gray-600">"Los juegos interactivos hacen que aprender sea divertido. Mi familia completa está aprendiendo juntos."</p>
                </div>
            </div>
        </div>
    </section>

    <!-- Gallery Carousel Section -->
    <section class="py-16 gradient-primary">
        <div class="container mx-auto px-4">
            <div class="text-center mb-12">
                <h2 class="text-4xl font-bold text-white mb-4">Galería de Momentos</h2>
                <p class="text-xl text-lighter-green">Capturando la alegría del aprendizaje</p>
            </div>

            <div class="relative max-w-6xl mx-auto">
                <div id="gallery-carousel" class="overflow-hidden rounded-2xl">
                    <div class="flex transition-transform duration-500 ease-in-out" id="gallery-inner">
                        <div class="w-full tablet:w-1/2 laptop:w-1/3 flex-shrink-0 px-2">
                            <div class="bg-white rounded-xl p-4 h-64 flex items-center justify-center">
                                <div class="text-center">
                                    <i class="fas fa-camera text-6xl text-primary mb-4"></i>
                                    <p class="text-gray-600">Estudiantes Practicando</p>
                                </div>
                            </div>
                        </div>
                        <div class="w-full tablet:w-1/2 laptop:w-1/3 flex-shrink-0 px-2">
                            <div class="bg-white rounded-xl p-4 h-64 flex items-center justify-center">
                                <div class="text-center">
                                    <i class="fas fa-graduation-cap text-6xl text-secondary mb-4"></i>
                                    <p class="text-gray-600">Ceremonia de Graduación</p>
                                </div>
                            </div>
                        </div>
                        <div class="w-full tablet:w-1/2 laptop:w-1/3 flex-shrink-0 px-2">
                            <div class="bg-white rounded-xl p-4 h-64 flex items-center justify-center">
                                <div class="text-center">
                                    <i class="fas fa-trophy text-6xl text-accent mb-4"></i>
                                    <p class="text-gray-600">Competencias de Señas</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Gallery Controls -->
                <button id="gallery-prev" class="absolute left-4 top-1/2 transform -translate-y-1/2 bg-white/20 hover:bg-white/30 text-white p-3 rounded-full transition-all">
                    <i class="fas fa-chevron-left"></i>
                </button>
                <button id="gallery-next" class="absolute right-4 top-1/2 transform -translate-y-1/2 bg-white/20 hover:bg-white/30 text-white p-3 rounded-full transition-all">
                    <i class="fas fa-chevron-right"></i>
                </button>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="gradient-primary text-white py-12">
        <div class="container mx-auto px-4">
            <div class="responsive-grid">
                <!-- Logo and Description -->
                <div class="tablet:col-span-2">
                    <div class="flex items-center space-x-3 mb-4">
                        <div class="w-12 h-12 bg-white rounded-full flex items-center justify-center">
                            <i class="fas fa-paw text-primary text-xl"></i>
                        </div>
                        <h3 class="text-2xl font-bold">AnimPlanetas</h3>
                    </div>
                    <p class="text-lighter-green mb-4 max-w-md">
                        Conectando el mundo animal con el lenguaje de señas. Educación inclusiva y accesible para todos.
                    </p>
                    <div class="flex space-x-4">
                        <a href="#" class="text-lighter-green hover:text-white transition-colors">
                            <i class="fab fa-facebook text-2xl"></i>
                        </a>
                        <a href="#" class="text-lighter-green hover:text-white transition-colors">
                            <i class="fab fa-twitter text-2xl"></i>
                        </a>
                        <a href="#" class="text-lighter-green hover:text-white transition-colors">
                            <i class="fab fa-instagram text-2xl"></i>
                        </a>
                        <a href="#" class="text-lighter-green hover:text-white transition-colors">
                            <i class="fab fa-youtube text-2xl"></i>
                        </a>
                    </div>
                </div>

                <!-- Quick Links -->
                <div>
                    <h4 class="text-lg font-bold mb-4">Enlaces Rápidos</h4>
                    <ul class="space-y-2">
                        <li><a href="#" class="text-lighter-green hover:text-white transition-colors">Inicio</a></li>
                        <li><a href="#" class="text-lighter-green hover:text-white transition-colors">AnimaleSeñas</a></li>
                        <li><a href="#" class="text-lighter-green hover:text-white transition-colors">PlanetaSeñas</a></li>
                        <li><a href="#" class="text-lighter-green hover:text-white transition-colors">Diccionario</a></li>
                        <li><a href="#" class="text-lighter-green hover:text-white transition-colors">Juegos</a></li>
                    </ul>
                </div>

                <!-- Contact Info -->
                <div>
                    <h4 class="text-lg font-bold mb-4">Contacto</h4>
                    <ul class="space-y-2">
                        <li class="flex items-center space-x-2">
                            <i class="fas fa-envelope text-lighter-green"></i>
                            <span class="text-lighter-green"><EMAIL></span>
                        </li>
                        <li class="flex items-center space-x-2">
                            <i class="fas fa-phone text-lighter-green"></i>
                            <span class="text-lighter-green">+****************</span>
                        </li>
                        <li class="flex items-center space-x-2">
                            <i class="fas fa-map-marker-alt text-lighter-green"></i>
                            <span class="text-lighter-green">Ciudad, País</span>
                        </li>
                    </ul>
                </div>
            </div>

            <!-- Bottom Footer -->
            <div class="border-t border-accent/30 mt-8 pt-8 text-center">
                <p class="text-lighter-green">
                    © 2025 AnimPlanetas. Todos los derechos reservados. |
                    <a href="#" class="hover:text-white transition-colors">Política de Privacidad</a> |
                    <a href="#" class="hover:text-white transition-colors">Términos de Servicio</a>
                </p>
            </div>
        </div>
    </footer>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            console.log('AnimPlanetas Sistema de Pestañas: Inicializando...');

            // Tab System Functionality
            const tabButtons = document.querySelectorAll('.tab-button');
            const tabContents = document.querySelectorAll('.tab-content');

            function switchTab(targetTab) {
                // Remove active class from all buttons and contents
                tabButtons.forEach(btn => btn.classList.remove('active'));
                tabContents.forEach(content => content.classList.remove('active'));

                // Add active class to clicked button and corresponding content
                const activeButton = document.querySelector(`[data-tab="${targetTab}"]`);
                const activeContent = document.getElementById(targetTab);

                if (activeButton && activeContent) {
                    activeButton.classList.add('active');
                    activeContent.classList.add('active');

                    // Trigger animation
                    activeContent.querySelector('.animate-fade-in-up').style.animation = 'none';
                    setTimeout(() => {
                        activeContent.querySelector('.animate-fade-in-up').style.animation = 'fadeInUp 0.6s ease-out';
                    }, 10);
                }
            }

            // Add click event listeners to tab buttons
            tabButtons.forEach(button => {
                button.addEventListener('click', function() {
                    const targetTab = this.getAttribute('data-tab');
                    switchTab(targetTab);
                });
            });

            // Mobile Menu Functionality
            const mobileMenuBtn = document.getElementById('mobile-menu-btn');
            const mobileMenu = document.getElementById('mobile-menu');
            const closeMobileMenu = document.getElementById('close-mobile-menu');

            function toggleMobileMenu() {
                mobileMenu.classList.toggle('hidden');
            }

            if (mobileMenuBtn) {
                mobileMenuBtn.addEventListener('click', toggleMobileMenu);
            }
            if (closeMobileMenu) {
                closeMobileMenu.addEventListener('click', toggleMobileMenu);
            }

            // Mobile dropdown functionality
            const mobileNavItems = document.querySelectorAll('.mobile-nav-item button');
            mobileNavItems.forEach(button => {
                button.addEventListener('click', function() {
                    const dropdown = this.nextElementSibling;
                    const chevron = this.querySelector('.fa-chevron-down');

                    if (dropdown && chevron) {
                        dropdown.classList.toggle('active');
                        chevron.style.transform = dropdown.classList.contains('active') ? 'rotate(180deg)' : 'rotate(0deg)';
                    }
                });
            });

            // Gallery Carousel Functionality
            const galleryInner = document.getElementById('gallery-inner');
            const galleryPrev = document.getElementById('gallery-prev');
            const galleryNext = document.getElementById('gallery-next');

            let galleryCurrentSlide = 0;
            const galleryTotalSlides = 3;

            function updateGallery() {
                if (galleryInner) {
                    const slideWidth = window.innerWidth >= 1024 ? 33.333 : window.innerWidth >= 768 ? 50 : 100;
                    const translateX = -galleryCurrentSlide * slideWidth;
                    galleryInner.style.transform = `translateX(${translateX}%)`;
                }
            }

            function nextGallerySlide() {
                galleryCurrentSlide = (galleryCurrentSlide + 1) % galleryTotalSlides;
                updateGallery();
            }

            function prevGallerySlide() {
                galleryCurrentSlide = (galleryCurrentSlide - 1 + galleryTotalSlides) % galleryTotalSlides;
                updateGallery();
            }

            if (galleryNext) {
                galleryNext.addEventListener('click', nextGallerySlide);
            }
            if (galleryPrev) {
                galleryPrev.addEventListener('click', prevGallerySlide);
            }

            // Auto-play gallery
            setInterval(nextGallerySlide, 4000);

            // Responsive gallery update
            window.addEventListener('resize', updateGallery);

            // Smooth scrolling for anchor links
            document.querySelectorAll('a[href^="#"]').forEach(anchor => {
                anchor.addEventListener('click', function (e) {
                    e.preventDefault();
                    const target = document.querySelector(this.getAttribute('href'));
                    if (target) {
                        target.scrollIntoView({
                            behavior: 'smooth',
                            block: 'start'
                        });
                    }
                });
            });

            // Intersection Observer for animations
            const observerOptions = {
                threshold: 0.1,
                rootMargin: '0px 0px -50px 0px'
            };

            const observer = new IntersectionObserver(function(entries) {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        entry.target.classList.add('animate-fade-in-up');
                    }
                });
            }, observerOptions);

            // Observe all sections for animations
            document.querySelectorAll('section').forEach(section => {
                observer.observe(section);
            });

            // Shopping cart functionality
            const addToCartButtons = document.querySelectorAll('[data-product]');
            let cartItems = [];

            addToCartButtons.forEach(button => {
                button.addEventListener('click', function() {
                    const productName = this.getAttribute('data-product');
                    const productPrice = this.getAttribute('data-price');

                    // Add item to cart (simplified)
                    cartItems.push({ name: productName, price: productPrice });

                    // Show notification
                    showNotification(`${productName} agregado al carrito`);

                    // Update cart counter
                    updateCartCounter();
                });
            });

            function showNotification(message) {
                // Create notification element
                const notification = document.createElement('div');
                notification.className = 'fixed top-4 right-4 bg-primary text-white px-6 py-3 rounded-lg shadow-lg z-50 transform translate-x-full transition-transform duration-300';
                notification.textContent = message;

                document.body.appendChild(notification);

                // Show notification
                setTimeout(() => {
                    notification.classList.remove('translate-x-full');
                }, 100);

                // Hide notification after 3 seconds
                setTimeout(() => {
                    notification.classList.add('translate-x-full');
                    setTimeout(() => {
                        document.body.removeChild(notification);
                    }, 300);
                }, 3000);
            }

            function updateCartCounter() {
                const cartCounter = document.querySelector('.fa-shopping-cart + span');
                if (cartCounter) {
                    cartCounter.textContent = cartItems.length;
                }
            }

            // Keyboard navigation
            document.addEventListener('keydown', function(e) {
                // Tab navigation with arrow keys
                if (e.key === 'ArrowLeft' || e.key === 'ArrowRight') {
                    const activeTab = document.querySelector('.tab-button.active');
                    if (activeTab) {
                        const allTabs = Array.from(tabButtons);
                        const currentIndex = allTabs.indexOf(activeTab);
                        let newIndex;

                        if (e.key === 'ArrowLeft') {
                            newIndex = currentIndex > 0 ? currentIndex - 1 : allTabs.length - 1;
                        } else {
                            newIndex = currentIndex < allTabs.length - 1 ? currentIndex + 1 : 0;
                        }

                        const newTab = allTabs[newIndex].getAttribute('data-tab');
                        switchTab(newTab);
                    }
                }

                // Escape key to close mobile menu
                if (e.key === 'Escape') {
                    if (!mobileMenu.classList.contains('hidden')) {
                        toggleMobileMenu();
                    }
                }
            });

            // Initialize gallery
            updateGallery();

            console.log('AnimPlanetas Sistema de Pestañas: ¡Inicialización completa!');
        });
    </script>
</body>
</html>