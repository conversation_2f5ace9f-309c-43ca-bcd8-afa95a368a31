/**
 * Parallax.js - Efecto parallax accesible y optimizado para AnimPlanetaS
 * Este script implementa efectos parallax que funcionan tanto en dispositivos móviles como de escritorio
 * y respeta las preferencias de reducción de movimiento para accesibilidad.
 */

class Parallax {
    /**
     * Constructor de la clase Parallax
     * @param {Object} options - Opciones de configuración
     */
    constructor(options = {}) {
        // Opciones por defecto
        this.defaults = {
            selector: '.parallax-element',
            speed: 0.5,
            direction: 'vertical', // vertical, horizontal
            mobileDisabled: true,
            respectReducedMotion: true,
            threshold: 0.2, // Umbral de intersección para iniciar el efecto
            zIndex: -1, // Valor z-index para elementos parallax
        };
        
        // Combinar opciones por defecto con las proporcionadas
        this.options = { ...this.defaults, ...options };
        
        // Elementos parallax
        this.elements = [];
        
        // Estado
        this.initialized = false;
        this.prefersReducedMotion = false;
        this.isMobile = false;
        
        // Inicializar
        this.init();
    }
    
    /**
     * Inicializar el efecto parallax
     */
    init() {
        // Verificar si el usuario prefiere reducción de movimiento
        this.checkReducedMotion();
        
        // Verificar si es un dispositivo móvil
        this.checkMobile();
        
        // Seleccionar elementos
        this.selectElements();
        
        // Si no hay elementos, salir
        if (this.elements.length === 0) {
            return;
        }
        
        // Configurar elementos
        this.setupElements();
        
        // Configurar observador de intersección
        this.setupIntersectionObserver();
        
        // Configurar eventos
        this.setupEvents();
        
        // Marcar como inicializado
        this.initialized = true;
    }
    
    /**
     * Verificar si el usuario prefiere reducción de movimiento
     */
    checkReducedMotion() {
        if (window.matchMedia) {
            this.prefersReducedMotion = window.matchMedia('(prefers-reduced-motion: reduce)').matches;
        }
    }
    
    /**
     * Verificar si es un dispositivo móvil
     */
    checkMobile() {
        this.isMobile = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent) || window.innerWidth < 768;
    }
    
    /**
     * Seleccionar elementos parallax
     */
    selectElements() {
        this.elements = Array.from(document.querySelectorAll(this.options.selector));
    }
    
    /**
     * Configurar elementos parallax
     */
    setupElements() {
        this.elements.forEach(element => {
            // Obtener opciones específicas del elemento
            const speed = parseFloat(element.dataset.parallaxSpeed) || this.options.speed;
            const direction = element.dataset.parallaxDirection || this.options.direction;
            const zIndex = element.dataset.parallaxZindex || this.options.zIndex;
            
            // Guardar opciones en el elemento
            element.parallaxOptions = {
                speed,
                direction,
                zIndex,
                initialTransform: window.getComputedStyle(element).transform,
            };
            
            // Configurar estilo inicial
            element.style.willChange = 'transform';
            element.style.transition = 'transform 0.1s linear';
            if (zIndex !== 'auto') {
                element.style.zIndex = zIndex;
            }
        });
    }
    
    /**
     * Configurar observador de intersección
     */
    setupIntersectionObserver() {
        const options = {
            root: null,
            rootMargin: '20px',
            threshold: this.options.threshold,
        };
        
        this.observer = new IntersectionObserver(entries => {
            entries.forEach(entry => {
                const element = entry.target;
                element.isVisible = entry.isIntersecting;
            });
        }, options);
        
        this.elements.forEach(element => {
            this.observer.observe(element);
        });
    }
    
    /**
     * Configurar eventos
     */
    setupEvents() {
        // Evento de scroll
        window.addEventListener('scroll', this.handleScroll.bind(this), { passive: true });
        
        // Evento de resize
        window.addEventListener('resize', this.handleResize.bind(this), { passive: true });
        
        // Evento de cambio de preferencias de movimiento
        if (window.matchMedia) {
            window.matchMedia('(prefers-reduced-motion: reduce)').addEventListener('change', this.handleReducedMotionChange.bind(this));
        }
        
        // Ejecutar una vez para posicionar inicialmente
        this.handleScroll();
    }
    
    /**
     * Manejar evento de scroll
     */
    handleScroll() {
        // Si el usuario prefiere reducción de movimiento y la opción está activada, salir
        if (this.prefersReducedMotion && this.options.respectReducedMotion) {
            return;
        }
        
        // Si está desactivado en móviles y es un dispositivo móvil, salir
        if (this.options.mobileDisabled && this.isMobile) {
            return;
        }
        
        // Calcular posición para cada elemento
        this.elements.forEach(element => {
            // Si el elemento no es visible, salir
            if (!element.isVisible) {
                return;
            }
            
            const rect = element.getBoundingClientRect();
            const viewportHeight = window.innerHeight;
            const viewportWidth = window.innerWidth;
            
            // Calcular posición relativa en la ventana (de -1 a 1)
            const centerY = rect.top + rect.height / 2;
            const centerX = rect.left + rect.width / 2;
            const relativeY = (centerY - viewportHeight / 2) / (viewportHeight / 2);
            const relativeX = (centerX - viewportWidth / 2) / (viewportWidth / 2);
            
            // Calcular desplazamiento
            let translateX = 0;
            let translateY = 0;
            
            if (element.parallaxOptions.direction === 'vertical') {
                translateY = relativeY * element.parallaxOptions.speed * -30;
            } else if (element.parallaxOptions.direction === 'horizontal') {
                translateX = relativeX * element.parallaxOptions.speed * -30;
            } else if (element.parallaxOptions.direction === 'both') {
                translateY = relativeY * element.parallaxOptions.speed * -30;
                translateX = relativeX * element.parallaxOptions.speed * -30;
            }
            
            // Aplicar transformación
            element.style.transform = `translate3d(${translateX}px, ${translateY}px, 0)`;
        });
    }
    
    /**
     * Manejar evento de resize
     */
    handleResize() {
        // Verificar si es un dispositivo móvil
        this.checkMobile();
        
        // Actualizar posiciones
        this.handleScroll();
    }
    
    /**
     * Manejar cambio en preferencias de reducción de movimiento
     */
    handleReducedMotionChange(event) {
        this.prefersReducedMotion = event.matches;
        
        // Si el usuario prefiere reducción de movimiento, resetear transformaciones
        if (this.prefersReducedMotion && this.options.respectReducedMotion) {
            this.resetTransforms();
        } else {
            // Si no, actualizar posiciones
            this.handleScroll();
        }
    }
    
    /**
     * Resetear transformaciones
     */
    resetTransforms() {
        this.elements.forEach(element => {
            element.style.transform = element.parallaxOptions.initialTransform || 'none';
        });
    }
    
    /**
     * Destruir instancia
     */
    destroy() {
        // Remover eventos
        window.removeEventListener('scroll', this.handleScroll.bind(this));
        window.removeEventListener('resize', this.handleResize.bind(this));
        
        if (window.matchMedia) {
            window.matchMedia('(prefers-reduced-motion: reduce)').removeEventListener('change', this.handleReducedMotionChange.bind(this));
        }
        
        // Desconectar observador
        if (this.observer) {
            this.observer.disconnect();
        }
        
        // Resetear transformaciones
        this.resetTransforms();
        
        // Limpiar referencias
        this.elements = [];
        this.initialized = false;
    }
}

// Inicializar automáticamente cuando el DOM esté listo
document.addEventListener('DOMContentLoaded', () => {
    // Crear instancia global
    window.parallaxInstance = new Parallax();
});