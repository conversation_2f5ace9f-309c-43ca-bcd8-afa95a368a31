<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AnimPlanetas - <PERSON><PERSON><PERSON>ñas</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        'primary': '#1D5D20',
                        'secondary': '#437845',
                        'accent': '#68936A',
                        'light-green': '#8EAE90',
                        'lighter-green': '#B4C9B5',
                        'lightest-green': '#D9E4DA'
                    }
                }
            }
        }
    </script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="styles.css" rel="stylesheet">
    <style>
        .sidebar-curve {
            clip-path: polygon(0 0, calc(100% - 20px) 0, 100% 20px, 100% 100%, 0 100%);
        }
        .dropdown-enter {
            opacity: 0;
            transform: translateY(-10px);
        }
        .dropdown-enter-active {
            opacity: 1;
            transform: translateY(0);
            transition: all 0.3s ease;
        }
        .nav-item:hover {
            transform: translateX(5px);
            transition: transform 0.3s ease;
        }
    </style>
</head>
<body class="bg-lightest-green min-h-screen">
    <!-- Sidebar -->
    <div id="sidebar" class="fixed left-0 top-0 h-full w-80 bg-gradient-to-b from-primary to-secondary sidebar-curve transform -translate-x-full transition-transform duration-300 ease-in-out z-50 shadow-2xl">
        <!-- Logo Section -->
        <div class="p-6 border-b border-accent/30">
            <div class="flex items-center space-x-3">
                <div class="w-12 h-12 bg-white rounded-full flex items-center justify-center">
                    <i class="fas fa-paw text-primary text-xl"></i>
                </div>
                <h1 class="text-white text-xl font-bold">AnimPlanetas</h1>
            </div>
        </div>

        <!-- Navigation Links -->
        <nav class="p-4 space-y-2">
            <!-- Inicio -->
            <div class="nav-item">
                <button class="nav-button w-full flex items-center justify-between p-3 text-white hover:bg-accent/20 rounded-lg transition-all duration-200" data-dropdown="inicio">
                    <div class="flex items-center space-x-3">
                        <i class="fas fa-home text-lg"></i>
                        <span>Inicio</span>
                    </div>
                    <i class="fas fa-chevron-down transition-transform duration-200"></i>
                </button>
                <div id="dropdown-inicio" class="dropdown hidden ml-6 mt-2 space-y-1">
                    <a href="#" class="block p-2 text-lighter-green hover:text-white transition-colors">
                        <i class="fas fa-video mr-2"></i>Video de Señas
                    </a>
                </div>
            </div>

            <!-- AnimaleSeñas -->
            <div class="nav-item">
                <button class="nav-button w-full flex items-center justify-between p-3 text-white hover:bg-accent/20 rounded-lg transition-all duration-200" data-dropdown="animales">
                    <div class="flex items-center space-x-3">
                        <i class="fas fa-dog text-lg"></i>
                        <span>AnimaleSeñas</span>
                    </div>
                    <i class="fas fa-chevron-down transition-transform duration-200"></i>
                </button>
                <div id="dropdown-animales" class="dropdown hidden ml-6 mt-2 space-y-1">
                    <a href="#" class="block p-2 text-lighter-green hover:text-white transition-colors">
                        <i class="fas fa-video mr-2"></i>Video AnimaleSeñas
                    </a>
                </div>
            </div>

            <!-- PlanetaSeñas -->
            <div class="nav-item">
                <button class="nav-button w-full flex items-center justify-between p-3 text-white hover:bg-accent/20 rounded-lg transition-all duration-200" data-dropdown="planeta">
                    <div class="flex items-center space-x-3">
                        <i class="fas fa-globe text-lg"></i>
                        <span>PlanetaSeñas</span>
                    </div>
                    <i class="fas fa-chevron-down transition-transform duration-200"></i>
                </button>
                <div id="dropdown-planeta" class="dropdown hidden ml-6 mt-2 space-y-1">
                    <a href="#" class="block p-2 text-lighter-green hover:text-white transition-colors">
                        <i class="fas fa-video mr-2"></i>Video PlanetaSeñas
                    </a>
                </div>
            </div>

            <!-- DiccionarioSeñas -->
            <div class="nav-item">
                <button class="nav-button w-full flex items-center justify-between p-3 text-white hover:bg-accent/20 rounded-lg transition-all duration-200" data-dropdown="diccionario">
                    <div class="flex items-center space-x-3">
                        <i class="fas fa-book text-lg"></i>
                        <span>DiccionarioSeñas</span>
                    </div>
                    <i class="fas fa-chevron-down transition-transform duration-200"></i>
                </button>
                <div id="dropdown-diccionario" class="dropdown hidden ml-6 mt-2 space-y-1">
                    <a href="#" class="block p-2 text-lighter-green hover:text-white transition-colors">
                        <i class="fas fa-video mr-2"></i>Video DiccionarioSeñas
                    </a>
                </div>
            </div>

            <!-- JuegoSeñas -->
            <div class="nav-item">
                <button class="nav-button w-full flex items-center justify-between p-3 text-white hover:bg-accent/20 rounded-lg transition-all duration-200" data-dropdown="juego">
                    <div class="flex items-center space-x-3">
                        <i class="fas fa-gamepad text-lg"></i>
                        <span>JuegoSeñas</span>
                    </div>
                    <i class="fas fa-chevron-down transition-transform duration-200"></i>
                </button>
                <div id="dropdown-juego" class="dropdown hidden ml-6 mt-2 space-y-1">
                    <a href="#" class="block p-2 text-lighter-green hover:text-white transition-colors">
                        <i class="fas fa-video mr-2"></i>Video JuegoSeñas
                    </a>
                </div>
            </div>

            <!-- VideoSeñas -->
            <div class="nav-item">
                <button class="nav-button w-full flex items-center justify-between p-3 text-white hover:bg-accent/20 rounded-lg transition-all duration-200" data-dropdown="video">
                    <div class="flex items-center space-x-3">
                        <i class="fas fa-play-circle text-lg"></i>
                        <span>VideoSeñas</span>
                    </div>
                    <i class="fas fa-chevron-down transition-transform duration-200"></i>
                </button>
                <div id="dropdown-video" class="dropdown hidden ml-6 mt-2 space-y-1">
                    <a href="#" class="block p-2 text-lighter-green hover:text-white transition-colors">
                        <i class="fas fa-video mr-2"></i>Video VideoSeñas
                    </a>
                </div>
            </div>

            <!-- ReflexiónSeñas -->
            <div class="nav-item">
                <button class="nav-button w-full flex items-center justify-between p-3 text-white hover:bg-accent/20 rounded-lg transition-all duration-200" data-dropdown="reflexion">
                    <div class="flex items-center space-x-3">
                        <i class="fas fa-lightbulb text-lg"></i>
                        <span>ReflexiónSeñas</span>
                    </div>
                    <i class="fas fa-chevron-down transition-transform duration-200"></i>
                </button>
                <div id="dropdown-reflexion" class="dropdown hidden ml-6 mt-2 space-y-1">
                    <a href="#" class="block p-2 text-lighter-green hover:text-white transition-colors">
                        <i class="fas fa-video mr-2"></i>Video ReflexiónSeñas
                    </a>
                </div>
            </div>

            <!-- TiendaSeñas -->
            <div class="nav-item">
                <button class="nav-button w-full flex items-center justify-between p-3 text-white hover:bg-accent/20 rounded-lg transition-all duration-200" data-dropdown="tienda">
                    <div class="flex items-center space-x-3">
                        <i class="fas fa-shopping-cart text-lg"></i>
                        <span>TiendaSeñas</span>
                    </div>
                    <i class="fas fa-chevron-down transition-transform duration-200"></i>
                </button>
                <div id="dropdown-tienda" class="dropdown hidden ml-6 mt-2 space-y-1">
                    <a href="#" class="block p-2 text-lighter-green hover:text-white transition-colors">
                        <i class="fas fa-video mr-2"></i>Video TiendaSeñas
                    </a>
                    <a href="#" class="block p-2 text-lighter-green hover:text-white transition-colors">
                        <i class="fas fa-shopping-cart mr-2"></i>Carrito
                    </a>
                </div>
            </div>
        </nav>

        <!-- Bottom Icons -->
        <div class="absolute bottom-6 left-6 right-6 border-t border-accent/30 pt-4">
            <div class="flex justify-center space-x-4">
                <button class="p-3 text-white hover:bg-accent/20 rounded-lg transition-all duration-200">
                    <i class="fas fa-user text-lg"></i>
                </button>
                <button class="p-3 text-white hover:bg-accent/20 rounded-lg transition-all duration-200">
                    <i class="fas fa-shopping-cart text-lg"></i>
                </button>
            </div>
        </div>
    </div>

    <!-- Main Content -->
    <div id="main-content" class="transition-all duration-300 ease-in-out">
        <!-- Header -->
        <header class="bg-white shadow-lg">
            <div class="container mx-auto px-4 py-4">
                <div class="flex items-center justify-between">
                    <!-- Left Side - Menu Button and Logo -->
                    <div class="flex items-center space-x-4">
                        <button id="menu-toggle" class="p-2 text-primary hover:bg-lightest-green rounded-lg transition-colors">
                            <i class="fas fa-bars text-xl"></i>
                        </button>
                        <div class="flex items-center space-x-3">
                            <div class="w-10 h-10 bg-primary rounded-full flex items-center justify-center">
                                <i class="fas fa-paw text-white"></i>
                            </div>
                            <h1 class="text-primary text-2xl font-bold">AnimPlanetas</h1>
                        </div>
                    </div>

                    <!-- Right Side Icons -->
                    <div class="flex items-center space-x-4">
                        <button class="p-2 text-primary hover:bg-lightest-green rounded-lg transition-colors">
                            <i class="fas fa-shopping-cart text-xl"></i>
                        </button>
                        <button class="p-2 text-primary hover:bg-lightest-green rounded-lg transition-colors">
                            <i class="fas fa-user text-xl"></i>
                        </button>
                    </div>
                </div>
            </div>
        </header>

        <!-- Hero Section with Carousel -->
        <section class="bg-gradient-to-r from-primary to-secondary py-20">
            <div class="container mx-auto px-4">
                <div class="text-center text-white mb-12">
                    <h2 class="text-5xl font-bold mb-4">Aprende Lenguaje de Señas</h2>
                    <p class="text-xl text-lighter-green">Conecta con el mundo animal a través del lenguaje de señas</p>
                </div>

                <!-- Carousel -->
                <div class="relative max-w-4xl mx-auto">
                    <div id="carousel" class="overflow-hidden rounded-2xl shadow-2xl">
                        <div class="flex transition-transform duration-500 ease-in-out" id="carousel-inner">
                            <div class="w-full flex-shrink-0">
                                <div class="bg-white p-8 text-center">
                                    <i class="fas fa-dog text-6xl text-primary mb-4"></i>
                                    <h3 class="text-2xl font-bold text-primary mb-2">AnimaleSeñas</h3>
                                    <p class="text-gray-600">Aprende las señas de tus animales favoritos</p>
                                </div>
                            </div>
                            <div class="w-full flex-shrink-0">
                                <div class="bg-white p-8 text-center">
                                    <i class="fas fa-globe text-6xl text-secondary mb-4"></i>
                                    <h3 class="text-2xl font-bold text-secondary mb-2">PlanetaSeñas</h3>
                                    <p class="text-gray-600">Explora el mundo de las señas planetarias</p>
                                </div>
                            </div>
                            <div class="w-full flex-shrink-0">
                                <div class="bg-white p-8 text-center">
                                    <i class="fas fa-gamepad text-6xl text-accent mb-4"></i>
                                    <h3 class="text-2xl font-bold text-accent mb-2">JuegoSeñas</h3>
                                    <p class="text-gray-600">Diviértete mientras aprendes</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Carousel Controls -->
                    <button id="prev-btn" class="absolute left-4 top-1/2 transform -translate-y-1/2 bg-white/20 hover:bg-white/30 text-white p-3 rounded-full transition-all">
                        <i class="fas fa-chevron-left"></i>
                    </button>
                    <button id="next-btn" class="absolute right-4 top-1/2 transform -translate-y-1/2 bg-white/20 hover:bg-white/30 text-white p-3 rounded-full transition-all">
                        <i class="fas fa-chevron-right"></i>
                    </button>

                    <!-- Carousel Indicators -->
                    <div class="flex justify-center mt-6 space-x-2">
                        <button class="carousel-indicator w-3 h-3 bg-white bg-opacity-50 rounded-full transition-all hover:bg-opacity-75" data-slide="0"></button>
                        <button class="carousel-indicator w-3 h-3 bg-white bg-opacity-50 rounded-full transition-all hover:bg-opacity-75" data-slide="1"></button>
                        <button class="carousel-indicator w-3 h-3 bg-white bg-opacity-50 rounded-full transition-all hover:bg-opacity-75" data-slide="2"></button>
                    </div>
                </div>
            </div>
        </section>

        <!-- CTA Section -->
        <section class="py-16 bg-white">
            <div class="container mx-auto px-4 text-center">
                <h2 class="text-4xl font-bold text-primary mb-6">¡Comienza tu Aventura!</h2>
                <p class="text-xl text-gray-600 mb-8 max-w-2xl mx-auto">
                    Únete a miles de personas que ya están aprendiendo el lenguaje de señas con AnimPlanetas
                </p>
                <div class="flex flex-col sm:flex-row gap-4 justify-center">
                    <button class="bg-primary hover:bg-secondary text-white px-8 py-4 rounded-lg text-lg font-semibold transition-all transform hover:scale-105">
                        Empezar Ahora
                    </button>
                    <button class="border-2 border-primary text-primary hover:bg-primary hover:text-white px-8 py-4 rounded-lg text-lg font-semibold transition-all">
                        Ver Demo
                    </button>
                </div>
            </div>
        </section>

        <!-- Statistics Section -->
        <section class="py-16 bg-lightest-green">
            <div class="container mx-auto px-4">
                <div class="grid grid-cols-1 md:grid-cols-4 gap-8 text-center">
                    <div class="bg-white p-6 rounded-xl shadow-lg">
                        <i class="fas fa-users text-4xl text-primary mb-4"></i>
                        <h3 class="text-3xl font-bold text-primary">10,000+</h3>
                        <p class="text-gray-600">Estudiantes Activos</p>
                    </div>
                    <div class="bg-white p-6 rounded-xl shadow-lg">
                        <i class="fas fa-video text-4xl text-secondary mb-4"></i>
                        <h3 class="text-3xl font-bold text-secondary">500+</h3>
                        <p class="text-gray-600">Videos de Señas</p>
                    </div>
                    <div class="bg-white p-6 rounded-xl shadow-lg">
                        <i class="fas fa-trophy text-4xl text-accent mb-4"></i>
                        <h3 class="text-3xl font-bold text-accent">95%</h3>
                        <p class="text-gray-600">Tasa de Éxito</p>
                    </div>
                    <div class="bg-white p-6 rounded-xl shadow-lg">
                        <i class="fas fa-star text-4xl text-light-green mb-4"></i>
                        <h3 class="text-3xl font-bold text-light-green">4.9/5</h3>
                        <p class="text-gray-600">Calificación</p>
                    </div>
                </div>
            </div>
        </section>

        <!-- Content/Blog Section -->
        <section class="py-16 bg-white">
            <div class="container mx-auto px-4">
                <div class="text-center mb-12">
                    <h2 class="text-4xl font-bold text-primary mb-4">Contenido Destacado</h2>
                    <p class="text-xl text-gray-600">Descubre nuestros artículos y recursos más populares</p>
                </div>

                <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
                    <article class="bg-lightest-green rounded-xl overflow-hidden shadow-lg hover:shadow-xl transition-shadow">
                        <div class="h-48 bg-gradient-to-br from-primary to-secondary flex items-center justify-center">
                            <i class="fas fa-paw text-6xl text-white"></i>
                        </div>
                        <div class="p-6">
                            <h3 class="text-xl font-bold text-primary mb-2">Señas Básicas de Animales</h3>
                            <p class="text-gray-600 mb-4">Aprende las señas fundamentales para comunicarte sobre animales domésticos.</p>
                            <a href="#" class="text-primary hover:text-secondary font-semibold">Leer más →</a>
                        </div>
                    </article>

                    <article class="bg-lightest-green rounded-xl overflow-hidden shadow-lg hover:shadow-xl transition-shadow">
                        <div class="h-48 bg-gradient-to-br from-secondary to-accent flex items-center justify-center">
                            <i class="fas fa-globe text-6xl text-white"></i>
                        </div>
                        <div class="p-6">
                            <h3 class="text-xl font-bold text-primary mb-2">El Mundo de las Señas</h3>
                            <p class="text-gray-600 mb-4">Explora cómo diferentes culturas utilizan el lenguaje de señas.</p>
                            <a href="#" class="text-primary hover:text-secondary font-semibold">Leer más →</a>
                        </div>
                    </article>

                    <article class="bg-lightest-green rounded-xl overflow-hidden shadow-lg hover:shadow-xl transition-shadow">
                        <div class="h-48 bg-gradient-to-br from-accent to-light-green flex items-center justify-center">
                            <i class="fas fa-heart text-6xl text-white"></i>
                        </div>
                        <div class="p-6">
                            <h3 class="text-xl font-bold text-primary mb-2">Reflexiones sobre Inclusión</h3>
                            <p class="text-gray-600 mb-4">La importancia del lenguaje de señas en la sociedad moderna.</p>
                            <a href="#" class="text-primary hover:text-secondary font-semibold">Leer más →</a>
                        </div>
                    </article>
                </div>
            </div>
        </section>

        <!-- Team Section -->
        <section class="py-16 bg-lightest-green">
            <div class="container mx-auto px-4">
                <div class="text-center mb-12">
                    <h2 class="text-4xl font-bold text-primary mb-4">Nuestro Equipo</h2>
                    <p class="text-xl text-gray-600">Conoce a los expertos detrás de AnimPlanetas</p>
                </div>

                <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
                    <div class="bg-white rounded-xl p-6 text-center shadow-lg hover:shadow-xl transition-shadow">
                        <div class="w-24 h-24 bg-gradient-to-br from-primary to-secondary rounded-full mx-auto mb-4 flex items-center justify-center">
                            <i class="fas fa-user text-3xl text-white"></i>
                        </div>
                        <h3 class="text-xl font-bold text-primary mb-2">María González</h3>
                        <p class="text-gray-600 mb-2">Especialista en Lenguaje de Señas</p>
                        <p class="text-sm text-gray-500">15 años de experiencia en educación inclusiva</p>
                    </div>

                    <div class="bg-white rounded-xl p-6 text-center shadow-lg hover:shadow-xl transition-shadow">
                        <div class="w-24 h-24 bg-gradient-to-br from-secondary to-accent rounded-full mx-auto mb-4 flex items-center justify-center">
                            <i class="fas fa-user text-3xl text-white"></i>
                        </div>
                        <h3 class="text-xl font-bold text-primary mb-2">Carlos Rodríguez</h3>
                        <p class="text-gray-600 mb-2">Desarrollador de Contenido</p>
                        <p class="text-sm text-gray-500">Experto en tecnología educativa</p>
                    </div>

                    <div class="bg-white rounded-xl p-6 text-center shadow-lg hover:shadow-xl transition-shadow">
                        <div class="w-24 h-24 bg-gradient-to-br from-accent to-light-green rounded-full mx-auto mb-4 flex items-center justify-center">
                            <i class="fas fa-user text-3xl text-white"></i>
                        </div>
                        <h3 class="text-xl font-bold text-primary mb-2">Ana Martínez</h3>
                        <p class="text-gray-600 mb-2">Diseñadora UX/UI</p>
                        <p class="text-sm text-gray-500">Especialista en accesibilidad digital</p>
                    </div>
                </div>
            </div>
        </section>

        <!-- Testimonials Section -->
        <section class="py-16 bg-white">
            <div class="container mx-auto px-4">
                <div class="text-center mb-12">
                    <h2 class="text-4xl font-bold text-primary mb-4">Lo que Dicen Nuestros Usuarios</h2>
                    <p class="text-xl text-gray-600">Testimonios reales de nuestra comunidad</p>
                </div>

                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                    <div class="bg-lightest-green p-6 rounded-xl shadow-lg">
                        <div class="flex items-center mb-4">
                            <div class="w-12 h-12 bg-primary rounded-full flex items-center justify-center mr-4">
                                <i class="fas fa-user text-white"></i>
                            </div>
                            <div>
                                <h4 class="font-bold text-primary">Laura Pérez</h4>
                                <div class="flex text-yellow-400">
                                    <i class="fas fa-star"></i>
                                    <i class="fas fa-star"></i>
                                    <i class="fas fa-star"></i>
                                    <i class="fas fa-star"></i>
                                    <i class="fas fa-star"></i>
                                </div>
                            </div>
                        </div>
                        <p class="text-gray-600">"AnimPlanetas ha transformado la manera en que mi hijo se comunica. Los videos son increíbles y muy fáciles de seguir."</p>
                    </div>

                    <div class="bg-lightest-green p-6 rounded-xl shadow-lg">
                        <div class="flex items-center mb-4">
                            <div class="w-12 h-12 bg-secondary rounded-full flex items-center justify-center mr-4">
                                <i class="fas fa-user text-white"></i>
                            </div>
                            <div>
                                <h4 class="font-bold text-primary">Roberto Silva</h4>
                                <div class="flex text-yellow-400">
                                    <i class="fas fa-star"></i>
                                    <i class="fas fa-star"></i>
                                    <i class="fas fa-star"></i>
                                    <i class="fas fa-star"></i>
                                    <i class="fas fa-star"></i>
                                </div>
                            </div>
                        </div>
                        <p class="text-gray-600">"Como profesor, recomiendo AnimPlanetas a todos mis colegas. Es una herramienta educativa excepcional."</p>
                    </div>

                    <div class="bg-lightest-green p-6 rounded-xl shadow-lg">
                        <div class="flex items-center mb-4">
                            <div class="w-12 h-12 bg-accent rounded-full flex items-center justify-center mr-4">
                                <i class="fas fa-user text-white"></i>
                            </div>
                            <div>
                                <h4 class="font-bold text-primary">Carmen López</h4>
                                <div class="flex text-yellow-400">
                                    <i class="fas fa-star"></i>
                                    <i class="fas fa-star"></i>
                                    <i class="fas fa-star"></i>
                                    <i class="fas fa-star"></i>
                                    <i class="fas fa-star"></i>
                                </div>
                            </div>
                        </div>
                        <p class="text-gray-600">"Los juegos interactivos hacen que aprender sea divertido. Mi familia completa está aprendiendo juntos."</p>
                    </div>
                </div>
            </div>
        </section>

        <!-- Gallery Carousel Section -->
        <section class="py-16 bg-gradient-to-r from-primary to-secondary">
            <div class="container mx-auto px-4">
                <div class="text-center mb-12">
                    <h2 class="text-4xl font-bold text-white mb-4">Galería de Momentos</h2>
                    <p class="text-xl text-lighter-green">Capturando la alegría del aprendizaje</p>
                </div>

                <div class="relative max-w-6xl mx-auto">
                    <div id="gallery-carousel" class="overflow-hidden rounded-2xl">
                        <div class="flex transition-transform duration-500 ease-in-out" id="gallery-inner">
                            <div class="w-full md:w-1/3 flex-shrink-0 px-2">
                                <div class="bg-white rounded-xl p-4 h-64 flex items-center justify-center">
                                    <div class="text-center">
                                        <i class="fas fa-camera text-6xl text-primary mb-4"></i>
                                        <p class="text-gray-600">Estudiantes Practicando</p>
                                    </div>
                                </div>
                            </div>
                            <div class="w-full md:w-1/3 flex-shrink-0 px-2">
                                <div class="bg-white rounded-xl p-4 h-64 flex items-center justify-center">
                                    <div class="text-center">
                                        <i class="fas fa-graduation-cap text-6xl text-secondary mb-4"></i>
                                        <p class="text-gray-600">Ceremonia de Graduación</p>
                                    </div>
                                </div>
                            </div>
                            <div class="w-full md:w-1/3 flex-shrink-0 px-2">
                                <div class="bg-white rounded-xl p-4 h-64 flex items-center justify-center">
                                    <div class="text-center">
                                        <i class="fas fa-trophy text-6xl text-accent mb-4"></i>
                                        <p class="text-gray-600">Competencias de Señas</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>
    </div>

    <!-- Footer -->
    <footer class="bg-primary text-white py-12">
        <div class="container mx-auto px-4">
            <div class="grid grid-cols-1 md:grid-cols-4 gap-8">
                <!-- Logo and Description -->
                <div class="col-span-1 md:col-span-2">
                    <div class="flex items-center space-x-3 mb-4">
                        <div class="w-12 h-12 bg-white rounded-full flex items-center justify-center">
                            <i class="fas fa-paw text-primary text-xl"></i>
                        </div>
                        <h3 class="text-2xl font-bold">AnimPlanetas</h3>
                    </div>
                    <p class="text-lighter-green mb-4 max-w-md">
                        Conectando el mundo animal con el lenguaje de señas. Educación inclusiva y accesible para todos.
                    </p>
                    <div class="flex space-x-4">
                        <a href="#" class="text-lighter-green hover:text-white transition-colors">
                            <i class="fab fa-facebook text-2xl"></i>
                        </a>
                        <a href="#" class="text-lighter-green hover:text-white transition-colors">
                            <i class="fab fa-twitter text-2xl"></i>
                        </a>
                        <a href="#" class="text-lighter-green hover:text-white transition-colors">
                            <i class="fab fa-instagram text-2xl"></i>
                        </a>
                        <a href="#" class="text-lighter-green hover:text-white transition-colors">
                            <i class="fab fa-youtube text-2xl"></i>
                        </a>
                    </div>
                </div>

                <!-- Quick Links -->
                <div>
                    <h4 class="text-lg font-bold mb-4">Enlaces Rápidos</h4>
                    <ul class="space-y-2">
                        <li><a href="#" class="text-lighter-green hover:text-white transition-colors">Inicio</a></li>
                        <li><a href="#" class="text-lighter-green hover:text-white transition-colors">AnimaleSeñas</a></li>
                        <li><a href="#" class="text-lighter-green hover:text-white transition-colors">PlanetaSeñas</a></li>
                        <li><a href="#" class="text-lighter-green hover:text-white transition-colors">Diccionario</a></li>
                        <li><a href="#" class="text-lighter-green hover:text-white transition-colors">Juegos</a></li>
                    </ul>
                </div>

                <!-- Contact Info -->
                <div>
                    <h4 class="text-lg font-bold mb-4">Contacto</h4>
                    <ul class="space-y-2">
                        <li class="flex items-center space-x-2">
                            <i class="fas fa-envelope text-lighter-green"></i>
                            <span class="text-lighter-green"><EMAIL></span>
                        </li>
                        <li class="flex items-center space-x-2">
                            <i class="fas fa-phone text-lighter-green"></i>
                            <span class="text-lighter-green">+****************</span>
                        </li>
                        <li class="flex items-center space-x-2">
                            <i class="fas fa-map-marker-alt text-lighter-green"></i>
                            <span class="text-lighter-green">Ciudad, País</span>
                        </li>
                    </ul>
                </div>
            </div>

            <!-- Bottom Footer -->
            <div class="border-t border-accent/30 mt-8 pt-8 text-center">
                <p class="text-lighter-green">
                    © 2025 AnimPlanetas. Todos los derechos reservados. |
                    <a href="#" class="hover:text-white transition-colors">Política de Privacidad</a> |
                    <a href="#" class="hover:text-white transition-colors">Términos de Servicio</a>
                </p>
            </div>
        </div>
    </footer>

    <!-- Overlay for sidebar -->
    <div id="sidebar-overlay" class="fixed inset-0 bg-black bg-opacity-50 z-40 hidden transition-opacity duration-300 opacity-0"></div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            console.log('AnimPlanetas: DOM loaded, initializing...');

            // Sidebar functionality
            const sidebar = document.getElementById('sidebar');
            const sidebarOverlay = document.getElementById('sidebar-overlay');
            const menuToggle = document.getElementById('menu-toggle');
            const mainContent = document.getElementById('main-content');

            console.log('Elements found:', {
                sidebar: !!sidebar,
                sidebarOverlay: !!sidebarOverlay,
                menuToggle: !!menuToggle,
                mainContent: !!mainContent
            });

            function toggleSidebar() {
                console.log('toggleSidebar called');
                if (sidebar && sidebarOverlay && mainContent) {
                    const isOpen = !sidebar.classList.contains('-translate-x-full');
                    console.log('Sidebar isOpen:', isOpen);

                    if (isOpen) {
                        // Close sidebar
                        console.log('Closing sidebar');
                        sidebar.classList.add('-translate-x-full');
                        sidebarOverlay.classList.add('hidden');
                        sidebarOverlay.classList.remove('opacity-100');
                        sidebarOverlay.classList.add('opacity-0');
                        if (window.innerWidth >= 1024) {
                            mainContent.classList.remove('ml-80');
                        }
                    } else {
                        // Open sidebar
                        console.log('Opening sidebar');
                        sidebar.classList.remove('-translate-x-full');
                        sidebarOverlay.classList.remove('hidden');
                        sidebarOverlay.classList.remove('opacity-0');
                        sidebarOverlay.classList.add('opacity-100');
                        if (window.innerWidth >= 1024) {
                            mainContent.classList.add('ml-80');
                        }
                    }
                } else {
                    console.log('Missing elements for sidebar toggle');
                }
            }

            if (menuToggle) {
                menuToggle.addEventListener('click', toggleSidebar);
            }
            if (sidebarOverlay) {
                sidebarOverlay.addEventListener('click', toggleSidebar);
            }

            // Dropdown functionality
            const navButtons = document.querySelectorAll('.nav-button');

            navButtons.forEach(button => {
                button.addEventListener('click', function() {
                    const dropdownId = this.getAttribute('data-dropdown');
                    const dropdown = document.getElementById(`dropdown-${dropdownId}`);
                    const chevron = this.querySelector('.fa-chevron-down');

                    if (dropdown && chevron) {
                        // Close all other dropdowns
                        document.querySelectorAll('.dropdown').forEach(d => {
                            if (d !== dropdown && !d.classList.contains('hidden')) {
                                d.classList.add('hidden');
                                const otherChevron = d.parentElement.querySelector('.fa-chevron-down');
                                if (otherChevron) {
                                    otherChevron.style.transform = 'rotate(0deg)';
                                }
                            }
                        });

                        // Toggle current dropdown
                        dropdown.classList.toggle('hidden');

                        if (dropdown.classList.contains('hidden')) {
                            chevron.style.transform = 'rotate(0deg)';
                        } else {
                            chevron.style.transform = 'rotate(180deg)';
                        }
                    }
                });
            });

            // Hero Carousel functionality
            const carousel = document.getElementById('carousel-inner');
            const prevBtn = document.getElementById('prev-btn');
            const nextBtn = document.getElementById('next-btn');
            const indicators = document.querySelectorAll('.carousel-indicator');

            let currentSlide = 0;
            const totalSlides = 3;

            function updateCarousel() {
                if (carousel) {
                    const translateX = -currentSlide * 100;
                    carousel.style.transform = `translateX(${translateX}%)`;

                    // Update indicators
                    indicators.forEach((indicator, index) => {
                        if (index === currentSlide) {
                            indicator.classList.remove('bg-white');
                            indicator.classList.remove('bg-opacity-50');
                            indicator.classList.add('bg-white');
                            indicator.classList.add('bg-opacity-100');
                        } else {
                            indicator.classList.remove('bg-white');
                            indicator.classList.remove('bg-opacity-100');
                            indicator.classList.add('bg-white');
                            indicator.classList.add('bg-opacity-50');
                        }
                    });
                }
            }

            function nextSlide() {
                currentSlide = (currentSlide + 1) % totalSlides;
                updateCarousel();
            }

            function prevSlide() {
                currentSlide = (currentSlide - 1 + totalSlides) % totalSlides;
                updateCarousel();
            }

            if (nextBtn) {
                nextBtn.addEventListener('click', nextSlide);
            }
            if (prevBtn) {
                prevBtn.addEventListener('click', prevSlide);
            }

            // Indicator click functionality
            indicators.forEach((indicator, index) => {
                indicator.addEventListener('click', () => {
                    currentSlide = index;
                    updateCarousel();
                });
            });

            // Auto-play carousel
            setInterval(nextSlide, 5000);

            // Initialize carousel
            updateCarousel();

            // Smooth scrolling for anchor links
            document.querySelectorAll('a[href^="#"]').forEach(anchor => {
                anchor.addEventListener('click', function (e) {
                    e.preventDefault();
                    const target = document.querySelector(this.getAttribute('href'));
                    if (target) {
                        target.scrollIntoView({
                            behavior: 'smooth',
                            block: 'start'
                        });
                    }
                });
            });

            // Add scroll effect to header
            window.addEventListener('scroll', function() {
                const header = document.querySelector('header');
                if (header) {
                    if (window.scrollY > 100) {
                        header.classList.add('shadow-xl');
                    } else {
                        header.classList.remove('shadow-xl');
                    }
                }
            });

            // Intersection Observer for animations
            const observerOptions = {
                threshold: 0.1,
                rootMargin: '0px 0px -50px 0px'
            };

            const observer = new IntersectionObserver(function(entries) {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        entry.target.classList.add('animate-fade-in');
                    }
                });
            }, observerOptions);

            // Observe all sections for animations
            document.querySelectorAll('section').forEach(section => {
                observer.observe(section);
            });

            // Add fade-in animation class
            const style = document.createElement('style');
            style.textContent = `
                .animate-fade-in {
                    animation: fadeIn 0.8s ease-in-out;
                }

                @keyframes fadeIn {
                    from {
                        opacity: 0;
                        transform: translateY(30px);
                    }
                    to {
                        opacity: 1;
                        transform: translateY(0);
                    }
                }
            `;
            document.head.appendChild(style);

            // Keyboard navigation
            document.addEventListener('keydown', function(e) {
                // Escape key to close sidebar
                if (e.key === 'Escape') {
                    const sidebar = document.getElementById('sidebar');
                    if (sidebar && !sidebar.classList.contains('-translate-x-full')) {
                        toggleSidebar();
                    }
                }

                // Arrow keys for carousel
                if (e.key === 'ArrowLeft') {
                    prevSlide();
                } else if (e.key === 'ArrowRight') {
                    nextSlide();
                }
            });

            console.log('AnimPlanetas: All functionality initialized successfully!');
        }); // End of DOMContentLoaded
    </script>
</body>
</html>
