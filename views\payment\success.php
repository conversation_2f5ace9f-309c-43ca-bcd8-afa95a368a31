<?php
/**
 * Confirmación exitosa de pago
 * Esta página redirige al método correspondiente en la clase Payment
 */

// Definir la ruta base del proyecto
define('ROOT_PATH', dirname(dirname(__DIR__)));

// Cargar la configuración
require_once ROOT_PATH . '/config/config.php';

// Iniciar sesión
session_start();

// Inicializar la conexión a la base de datos
$db = Database::getInstance()->getConnection();

// Crear instancia de la clase Auth para verificar acceso
$auth = new Auth($db);

// Verificar si el usuario está autenticado
if (!$auth->isLoggedIn()) {
    // Redirigir al login
    header('Location: /login.php');
    exit;
}

// Obtener los parámetros de la transacción
$paymentId = isset($_GET['payment_id']) ? $_GET['payment_id'] : null;
$status = isset($_GET['status']) ? $_GET['status'] : null;
$externalReference = isset($_GET['external_reference']) ? $_GET['external_reference'] : null;

if (!$paymentId || !$status || !$externalReference) {
    // Redirigir a la página principal si faltan parámetros
    header('Location: /');
    exit;
}

// Crear instancia de la clase Payment
$payment = new Payment($db);

// Procesar el pago exitoso
$payment->processSuccessfulPayment($paymentId, $status, $externalReference, $auth->getUserId());

// Mostrar la página de éxito
$payment->showSuccessPage($paymentId);