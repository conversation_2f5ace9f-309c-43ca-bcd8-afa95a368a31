/**
 * Archivo JavaScript para la barra de navegación de AnimPlanetaS
 * Contiene funcionalidades específicas para la navegación
 */

document.addEventListener('DOMContentLoaded', function() {
    initMobileMenu();
    initDropdownMenus();
    initActiveLinks();
    initScrollEffects();
});

/**
 * Inicializa el menú móvil
 */
function initMobileMenu() {
    const mobileMenuButton = document.getElementById('mobile-menu-button');
    const mobileMenu = document.getElementById('mobile-menu');
    
    if (mobileMenuButton && mobileMenu) {
        mobileMenuButton.addEventListener('click', function() {
            // Alternar la visibilidad del menú móvil
            const isExpanded = mobileMenuButton.getAttribute('aria-expanded') === 'true';
            mobileMenuButton.setAttribute('aria-expanded', !isExpanded);
            mobileMenu.classList.toggle('hidden');
            
            // Cambiar el ícono del botón
            const openIcon = mobileMenuButton.querySelector('.open-icon');
            const closeIcon = mobileMenuButton.querySelector('.close-icon');
            
            if (openIcon && closeIcon) {
                openIcon.classList.toggle('hidden');
                closeIcon.classList.toggle('hidden');
            }
            
            // Prevenir scroll cuando el menú está abierto
            document.body.classList.toggle('overflow-hidden', !isExpanded);
        });
        
        // Cerrar el menú al hacer clic fuera de él
        document.addEventListener('click', function(event) {
            const isMenuOpen = !mobileMenu.classList.contains('hidden');
            const isClickInsideMenu = mobileMenu.contains(event.target);
            const isClickOnMenuButton = mobileMenuButton.contains(event.target);
            
            if (isMenuOpen && !isClickInsideMenu && !isClickOnMenuButton) {
                mobileMenuButton.click();
            }
        });
    }
}

/**
 * Inicializa los menús desplegables
 */
function initDropdownMenus() {
    // Menú desplegable de escritorio
    const dropdownButtons = document.querySelectorAll('.dropdown-button');
    
    dropdownButtons.forEach(button => {
        const dropdown = button.nextElementSibling;
        
        if (dropdown) {
            // Mostrar/ocultar al hacer hover en escritorio
            if (window.innerWidth >= 768) { // md breakpoint
                button.addEventListener('mouseenter', () => {
                    dropdown.classList.remove('hidden');
                    button.setAttribute('aria-expanded', 'true');
                });
                
                button.parentElement.addEventListener('mouseleave', () => {
                    dropdown.classList.add('hidden');
                    button.setAttribute('aria-expanded', 'false');
                });
            }
            
            // Mostrar/ocultar al hacer clic (para móvil y accesibilidad)
            button.addEventListener('click', (e) => {
                e.preventDefault();
                const isExpanded = button.getAttribute('aria-expanded') === 'true';
                
                // Cerrar otros dropdowns
                dropdownButtons.forEach(otherButton => {
                    if (otherButton !== button) {
                        otherButton.setAttribute('aria-expanded', 'false');
                        if (otherButton.nextElementSibling) {
                            otherButton.nextElementSibling.classList.add('hidden');
                        }
                    }
                });
                
                dropdown.classList.toggle('hidden');
                button.setAttribute('aria-expanded', !isExpanded);
            });
            
            // Soporte para navegación por teclado
            button.addEventListener('keydown', (e) => {
                if (e.key === 'Enter' || e.key === ' ') {
                    e.preventDefault();
                    button.click();
                    
                    // Enfocar el primer elemento del menú
                    if (button.getAttribute('aria-expanded') === 'true') {
                        const firstItem = dropdown.querySelector('a');
                        if (firstItem) {
                            firstItem.focus();
                        }
                    }
                } else if (e.key === 'Escape') {
                    dropdown.classList.add('hidden');
                    button.setAttribute('aria-expanded', 'false');
                    button.focus();
                }
            });
        }
    });
    
    // Menú desplegable móvil
    const mobileDropdownButtons = document.querySelectorAll('.mobile-dropdown-button');
    
    mobileDropdownButtons.forEach(button => {
        const dropdown = button.nextElementSibling;
        
        if (dropdown) {
            button.addEventListener('click', (e) => {
                e.preventDefault();
                dropdown.classList.toggle('hidden');
                
                // Cambiar el ícono del botón
                const expandIcon = button.querySelector('.expand-icon');
                const collapseIcon = button.querySelector('.collapse-icon');
                
                if (expandIcon && collapseIcon) {
                    expandIcon.classList.toggle('hidden');
                    collapseIcon.classList.toggle('hidden');
                }
                
                // Actualizar atributo aria-expanded
                const isExpanded = dropdown.classList.contains('hidden') ? 'false' : 'true';
                button.setAttribute('aria-expanded', isExpanded);
            });
        }
    });
    
    // Cerrar dropdowns al hacer clic fuera
    document.addEventListener('click', (e) => {
        if (!e.target.closest('.dropdown-container')) {
            document.querySelectorAll('.dropdown-menu').forEach(menu => {
                menu.classList.add('hidden');
            });
            
            document.querySelectorAll('.dropdown-button').forEach(button => {
                button.setAttribute('aria-expanded', 'false');
            });
        }
    });
}

/**
 * Resalta los enlaces activos en la navegación
 */
function initActiveLinks() {
    const currentPath = window.location.pathname;
    const navLinks = document.querySelectorAll('.nav-link');
    
    navLinks.forEach(link => {
        const href = link.getAttribute('href');
        
        // Comprobar si el enlace coincide con la ruta actual
        if (href === currentPath || 
            (href !== '/' && currentPath.startsWith(href)) ||
            (currentPath === '/' && href === '/index.php')) {
            link.classList.add('active');
            link.setAttribute('aria-current', 'page');
            
            // Si está en un dropdown, marcar también el botón del dropdown
            const dropdownParent = link.closest('.dropdown-menu');
            if (dropdownParent) {
                const dropdownButton = dropdownParent.previousElementSibling;
                if (dropdownButton && dropdownButton.classList.contains('dropdown-button')) {
                    dropdownButton.classList.add('active');
                }
            }
        }
    });
}

/**
 * Inicializa efectos de scroll para la barra de navegación
 */
function initScrollEffects() {
    const navbar = document.querySelector('.navbar');
    
    if (navbar) {
        // Cambiar estilo al hacer scroll
        window.addEventListener('scroll', () => {
            if (window.scrollY > 10) {
                navbar.classList.add('navbar-scrolled');
            } else {
                navbar.classList.remove('navbar-scrolled');
            }
        });
        
        // Comprobar posición inicial
        if (window.scrollY > 10) {
            navbar.classList.add('navbar-scrolled');
        }
    }
}

/**
 * Función para desplazarse suavemente a un ancla
 * @param {string} targetId - ID del elemento destino
 */
function scrollToAnchor(targetId) {
    const targetElement = document.getElementById(targetId);
    
    if (targetElement) {
        // Calcular la posición de desplazamiento teniendo en cuenta la altura de la barra de navegación
        const navbar = document.querySelector('.navbar');
        const navbarHeight = navbar ? navbar.offsetHeight : 0;
        const targetPosition = targetElement.getBoundingClientRect().top + window.pageYOffset - navbarHeight;
        
        // Desplazarse suavemente
        window.scrollTo({
            top: targetPosition,
            behavior: 'smooth'
        });
    }
}

// Inicializar enlaces de anclaje
document.addEventListener('DOMContentLoaded', () => {
    const anchorLinks = document.querySelectorAll('a[href^="#"]:not([href="#"])');
    
    anchorLinks.forEach(link => {
        link.addEventListener('click', (e) => {
            const targetId = link.getAttribute('href').substring(1);
            
            if (document.getElementById(targetId)) {
                e.preventDefault();
                scrollToAnchor(targetId);
                
                // Cerrar el menú móvil si está abierto
                const mobileMenu = document.getElementById('mobile-menu');
                const mobileMenuButton = document.getElementById('mobile-menu-button');
                
                if (mobileMenu && !mobileMenu.classList.contains('hidden') && mobileMenuButton) {
                    mobileMenuButton.click();
                }
            }
        });
    });
    
    // Manejar enlaces de anclaje en la URL
    if (window.location.hash) {
        const targetId = window.location.hash.substring(1);
        
        // Esperar a que se cargue completamente la página
        setTimeout(() => {
            scrollToAnchor(targetId);
        }, 100);
    }
});

// Exportar funciones para uso global
window.NavBar = {
    scrollToAnchor
};