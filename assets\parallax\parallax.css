/**
 * Parallax.css - Estilos para efectos parallax en AnimPlanetaS
 * Estos estilos complementan la funcionalidad de parallax.js
 */

/* Contenedor principal para elementos parallax */
.parallax-container {
    position: relative;
    overflow: hidden;
    width: 100%;
    height: auto;
}

/* Elemento parallax básico */
.parallax-element {
    position: relative;
    will-change: transform;
}

/* Elemento parallax de fondo */
.parallax-background {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: -1;
    will-change: transform;
}

/* Elemento parallax con profundidad */
.parallax-depth-1 { --parallax-speed: 0.1; }
.parallax-depth-2 { --parallax-speed: 0.2; }
.parallax-depth-3 { --parallax-speed: 0.3; }
.parallax-depth-4 { --parallax-speed: 0.4; }
.parallax-depth-5 { --parallax-speed: 0.5; }

/* Variantes de dirección */
.parallax-vertical { --parallax-direction: vertical; }
.parallax-horizontal { --parallax-direction: horizontal; }
.parallax-both { --parallax-direction: both; }

/* Secciones con parallax */
.section-parallax {
    position: relative;
    overflow: hidden;
    padding: 6rem 0;
}

.section-parallax .parallax-background {
    transform: translateY(0);
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
}

/* Estilos específicos para héroes con parallax */
.hero-parallax {
    min-height: 80vh;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #fff;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5);
}

.hero-parallax .parallax-background {
    z-index: -1;
}

.hero-parallax .hero-content {
    position: relative;
    z-index: 1;
    text-align: center;
    max-width: 800px;
    padding: 2rem;
}

/* Elementos flotantes con parallax */
.parallax-float {
    position: absolute;
    will-change: transform;
}

.parallax-float-1 {
    top: 10%;
    left: 10%;
}

.parallax-float-2 {
    top: 20%;
    right: 15%;
}

.parallax-float-3 {
    bottom: 15%;
    left: 20%;
}

.parallax-float-4 {
    bottom: 25%;
    right: 10%;
}

/* Parallax para tarjetas */
.card-parallax {
    transition: transform 0.3s ease;
}

.card-parallax:hover {
    transform: translateY(-5px);
}

/* Soporte para preferencias de reducción de movimiento */
@media (prefers-reduced-motion: reduce) {
    .parallax-element,
    .parallax-background,
    .parallax-float,
    .card-parallax {
        transition: none !important;
        transform: none !important;
        will-change: auto;
    }
}

/* Desactivar en dispositivos móviles */
@media (max-width: 767px) {
    .parallax-mobile-disabled {
        transform: none !important;
        will-change: auto;
    }
}