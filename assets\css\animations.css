/**
 * Animaciones CSS para AnimPlanetaS
 * Efectos visuales y transiciones para mejorar la experiencia de usuario
 */

/* Efecto Gooey - para elementos fluidos y orgánicos */
.gooey {
    filter: url('#gooey-filter');
}

/* SVG para filtro gooey (debe incluirse en el HTML) */
/* 
<svg style="position: absolute; width: 0; height: 0;" xmlns="http://www.w3.org/2000/svg" version="1.1">
    <defs>
        <filter id="gooey-filter">
            <feGaussianBlur in="SourceGraphic" stdDeviation="10" result="blur" />
            <feColorMatrix in="blur" mode="matrix" values="1 0 0 0 0  0 1 0 0 0  0 0 1 0 0  0 0 0 19 -9" result="gooey" />
            <feComposite in="SourceGraphic" in2="gooey" operator="atop"/>
        </filter>
    </defs>
</svg>
*/

/* Animación de entrada con fade */
@keyframes fadeIn {
    from {
        opacity: 0;
    }
    to {
        opacity: 1;
    }
}

.animate-fade-in {
    animation: fadeIn 0.5s ease-in-out forwards;
}

/* Animación de entrada desde abajo */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.animate-fade-in-up {
    animation: fadeInUp 0.5s ease-in-out forwards;
}

/* Animación de entrada desde arriba */
@keyframes fadeInDown {
    from {
        opacity: 0;
        transform: translateY(-20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.animate-fade-in-down {
    animation: fadeInDown 0.5s ease-in-out forwards;
}

/* Animación de entrada desde la izquierda */
@keyframes fadeInLeft {
    from {
        opacity: 0;
        transform: translateX(-20px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

.animate-fade-in-left {
    animation: fadeInLeft 0.5s ease-in-out forwards;
}

/* Animación de entrada desde la derecha */
@keyframes fadeInRight {
    from {
        opacity: 0;
        transform: translateX(20px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

.animate-fade-in-right {
    animation: fadeInRight 0.5s ease-in-out forwards;
}

/* Animación de rebote */
@keyframes bounce {
    0%, 20%, 50%, 80%, 100% {
        transform: translateY(0);
    }
    40% {
        transform: translateY(-20px);
    }
    60% {
        transform: translateY(-10px);
    }
}

.animate-bounce {
    animation: bounce 1s ease infinite;
}

/* Animación de pulso */
@keyframes pulse {
    0% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.05);
    }
    100% {
        transform: scale(1);
    }
}

.animate-pulse {
    animation: pulse 2s ease infinite;
}

/* Animación de sacudida */
@keyframes shake {
    0%, 100% {
        transform: translateX(0);
    }
    10%, 30%, 50%, 70%, 90% {
        transform: translateX(-5px);
    }
    20%, 40%, 60%, 80% {
        transform: translateX(5px);
    }
}

.animate-shake {
    animation: shake 0.8s ease;
}

/* Animación de rotación */
@keyframes rotate {
    from {
        transform: rotate(0deg);
    }
    to {
        transform: rotate(360deg);
    }
}

.animate-rotate {
    animation: rotate 2s linear infinite;
}

/* Animación de carga (spinner) */
@keyframes spinner {
    0% {
        transform: rotate(0deg);
    }
    100% {
        transform: rotate(360deg);
    }
}

.loading-spinner {
    width: 40px;
    height: 40px;
    border: 4px solid rgba(0, 0, 0, 0.1);
    border-radius: 50%;
    border-top-color: var(--primary-color);
    animation: spinner 0.8s linear infinite;
}

/* Animación de desvanecimiento para cargas de página */
.page-transition {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: var(--background-dark);
    z-index: 9999;
    opacity: 0;
    pointer-events: none;
    transition: opacity 0.3s ease;
}

.page-transition.active {
    opacity: 1;
    pointer-events: all;
}

/* Efectos de hover para tarjetas */
.hover-lift {
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.hover-lift:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
}

/* Efecto de zoom en imágenes */
.hover-zoom {
    overflow: hidden;
}

.hover-zoom img {
    transition: transform 0.5s ease;
}

.hover-zoom:hover img {
    transform: scale(1.05);
}

/* Efecto de overlay en tarjetas */
.hover-overlay {
    position: relative;
    overflow: hidden;
}

.hover-overlay::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    opacity: 0;
    transition: opacity 0.3s ease;
    z-index: 1;
}

.hover-overlay:hover::before {
    opacity: 1;
}

.hover-overlay-content {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    z-index: 2;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.hover-overlay:hover .hover-overlay-content {
    opacity: 1;
}

/* Efecto de brillo */
.hover-shine {
    position: relative;
    overflow: hidden;
}

.hover-shine::before {
    content: '';
    position: absolute;
    top: 0;
    left: -75%;
    width: 50%;
    height: 100%;
    background: linear-gradient(to right, rgba(255, 255, 255, 0) 0%, rgba(255, 255, 255, 0.3) 100%);
    transform: skewX(-25deg);
    transition: all 0.75s ease;
}

.hover-shine:hover::before {
    left: 125%;
}

/* Efecto de borde animado */
.hover-border {
    position: relative;
}

.hover-border::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 0;
    height: 2px;
    background-color: var(--primary-color);
    transition: width 0.3s ease;
}

.hover-border:hover::after {
    width: 100%;
}

/* Animación de escritura de texto */
@keyframes typing {
    from { width: 0 }
    to { width: 100% }
}

@keyframes blink-caret {
    from, to { border-color: transparent }
    50% { border-color: var(--primary-color) }
}

.typewriter {
    overflow: hidden;
    border-right: 2px solid var(--primary-color);
    white-space: nowrap;
    margin: 0 auto;
    animation: 
        typing 3.5s steps(40, end),
        blink-caret 0.75s step-end infinite;
}

/* Animación de conteo numérico */
.count-up {
    counter-reset: count 0;
    animation: count-up 2s forwards;
}

@keyframes count-up {
    from {
        counter-increment: count 0;
    }
    to {
        counter-increment: count var(--target-count);
    }
}

.count-up::after {
    content: counter(count);
}

/* Animación de ondas */
@keyframes wave {
    0% {
        transform: translateY(0);
    }
    50% {
        transform: translateY(-10px);
    }
    100% {
        transform: translateY(0);
    }
}

.animate-wave {
    animation: wave 2s ease infinite;
}

/* Animación de ondas escalonadas para múltiples elementos */
.wave-group > * {
    animation: wave 2s ease infinite;
}

.wave-group > *:nth-child(2) {
    animation-delay: 0.2s;
}

.wave-group > *:nth-child(3) {
    animation-delay: 0.4s;
}

.wave-group > *:nth-child(4) {
    animation-delay: 0.6s;
}

.wave-group > *:nth-child(5) {
    animation-delay: 0.8s;
}

/* Animación de revelación de texto */
.reveal-text {
    position: relative;
    color: transparent;
}

.reveal-text::before {
    content: attr(data-text);
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    color: var(--text-color);
    overflow: hidden;
    white-space: nowrap;
    animation: reveal 2s ease forwards;
}

@keyframes reveal {
    from {
        width: 0;
    }
    to {
        width: 100%;
    }
}

/* Animación de desplazamiento para parallax */
.parallax-element {
    transition: transform 0.5s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    will-change: transform;
}

/* Animación de desenfoque al entrar */
@keyframes blurIn {
    from {
        opacity: 0;
        filter: blur(10px);
    }
    to {
        opacity: 1;
        filter: blur(0);
    }
}

.animate-blur-in {
    animation: blurIn 0.8s ease-out forwards;
}

/* Animación de despliegue */
@keyframes unfold {
    from {
        max-height: 0;
        opacity: 0;
    }
    to {
        max-height: 1000px;
        opacity: 1;
    }
}

.animate-unfold {
    overflow: hidden;
    animation: unfold 0.5s ease-out forwards;
}

/* Animación de despliegue con retraso para elementos anidados */
.unfold-group > * {
    opacity: 0;
    transform: translateY(10px);
    transition: opacity 0.5s ease, transform 0.5s ease;
}

.unfold-group.active > * {
    opacity: 1;
    transform: translateY(0);
}

.unfold-group.active > *:nth-child(2) {
    transition-delay: 0.1s;
}

.unfold-group.active > *:nth-child(3) {
    transition-delay: 0.2s;
}

.unfold-group.active > *:nth-child(4) {
    transition-delay: 0.3s;
}

.unfold-group.active > *:nth-child(5) {
    transition-delay: 0.4s;
}

/* Animación de progreso */
@keyframes progress {
    from {
        width: 0;
    }
    to {
        width: var(--progress-width, 100%);
    }
}

.animate-progress {
    animation: progress 1.5s ease-out forwards;
}

/* Animaciones para accesibilidad */
@media (prefers-reduced-motion: reduce) {
    * {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
        scroll-behavior: auto !important;
    }
}