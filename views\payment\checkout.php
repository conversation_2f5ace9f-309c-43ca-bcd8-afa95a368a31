<?php
/**
 * Página de proceso de pago
 * Esta página redirige al método correspondiente en la clase Payment
 */

// Definir la ruta base del proyecto
define('ROOT_PATH', dirname(dirname(__DIR__)));

// Cargar la configuración
require_once ROOT_PATH . '/config/config.php';

// Iniciar sesión
session_start();

// Inicializar la conexión a la base de datos
$db = Database::getInstance()->getConnection();

// Crear instancia de la clase Auth para verificar acceso
$auth = new Auth($db);

// Verificar si el usuario está autenticado
if (!$auth->isLoggedIn()) {
    // Redirigir al login con mensaje
    $_SESSION['redirect_after_login'] = '/views/payment/checkout.php';
    $_SESSION['auth_message'] = 'Debes iniciar sesión para realizar un pago';
    header('Location: /login.php');
    exit;
}

// Obtener el ID del plan de suscripción
$planId = isset($_GET['plan']) ? (int)$_GET['plan'] : null;

if (!$planId) {
    // Redirigir a la página de planes si no se especificó un plan
    header('Location: /plan.php');
    exit;
}

// Crear instancia de la clase Payment
$payment = new Payment($db);

// Mostrar la página de checkout
$payment->showCheckout($planId, $auth->getUserId());