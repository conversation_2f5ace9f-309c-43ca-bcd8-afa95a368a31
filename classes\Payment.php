<?php
/**
 * Clase Payment para el procesamiento de pagos
 */
class Payment {
    private $db;
    private $userModel;
    private $subscriptionModel;
    private $paymentModel;
    
    /**
     * Constructor
     */
    public function __construct() {
        $this->db = Database::getInstance()->getConnection();
        $this->userModel = new UserModel($this->db);
        $this->subscriptionModel = new SubscriptionModel($this->db);
        $this->paymentModel = new PaymentModel($this->db);
        
        // Inicializar MercadoPago
        require_once ROOT_PATH . '/vendor/autoload.php';
        \MercadoPago\SDK::setAccessToken(MP_ACCESS_TOKEN);
    }
    
    /**
     * Mostrar página de checkout
     */
    public function showCheckout() {
        // Verificar autenticación
        $auth = new Auth();
        if (!$auth->isAuthenticated()) {
            header('Location: /AnimPlanetaS/login?redirect=payment/checkout');
            exit;
        }
        
        // Obtener plan seleccionado
        $planId = isset($_GET['plan']) ? (int)$_GET['plan'] : null;
        
        if (!$planId) {
            header('Location: /AnimPlanetaS/plan');
            exit;
        }
        
        $plan = $this->subscriptionModel->getPlanById($planId);
        
        if (!$plan) {
            header('Location: /AnimPlanetaS/plan');
            exit;
        }
        
        // Obtener datos del usuario
        $userId = $_SESSION['user_id'];
        $user = $this->userModel->getUserById($userId);
        
        // Crear preferencia de pago en MercadoPago
        $preference = $this->createPaymentPreference($user, $plan);
        
        include VIEWS_PATH . '/payment/checkout.php';
    }
    
    /**
     * Procesar pago
     */
    public function processPayment() {
        // Verificar autenticación
        $auth = new Auth();
        if (!$auth->isAuthenticated()) {
            return ['success' => false, 'message' => 'Usuario no autenticado'];
        }
        
        // Obtener datos del pago
        $paymentId = $_POST['payment_id'] ?? null;
        $paymentStatus = $_POST['status'] ?? null;
        $paymentType = $_POST['payment_type'] ?? null;
        $merchantOrderId = $_POST['merchant_order_id'] ?? null;
        
        if (!$paymentId || !$paymentStatus) {
            return ['success' => false, 'message' => 'Datos de pago incompletos'];
        }
        
        // Verificar pago en MercadoPago
        $payment = $this->getPaymentInfo($paymentId);
        
        if (!$payment || $payment->status != $paymentStatus) {
            return ['success' => false, 'message' => 'Error al verificar el pago'];
        }
        
        // Obtener datos del usuario y plan
        $userId = $_SESSION['user_id'];
        $planId = $payment->metadata->plan_id;
        
        $user = $this->userModel->getUserById($userId);
        $plan = $this->subscriptionModel->getPlanById($planId);
        
        if (!$user || !$plan) {
            return ['success' => false, 'message' => 'Error al obtener datos de usuario o plan'];
        }
        
        // Registrar pago en la base de datos
        $paymentData = [
            'user_id' => $userId,
            'plan_id' => $planId,
            'payment_id' => $paymentId,
            'payment_type' => $paymentType,
            'amount' => $payment->transaction_amount,
            'currency' => $payment->currency_id,
            'status' => $paymentStatus,
            'merchant_order_id' => $merchantOrderId,
            'created_at' => date('Y-m-d H:i:s')
        ];
        
        $paymentRecordId = $this->paymentModel->createPayment($paymentData);
        
        if (!$paymentRecordId) {
            return ['success' => false, 'message' => 'Error al registrar el pago'];
        }
        
        // Si el pago es exitoso, activar suscripción
        if ($paymentStatus === 'approved') {
            // Calcular fecha de expiración según duración del plan
            $startDate = date('Y-m-d H:i:s');
            $endDate = $this->calculateSubscriptionEndDate($startDate, $plan['duration_months']);
            
            $subscriptionData = [
                'user_id' => $userId,
                'plan_id' => $planId,
                'payment_id' => $paymentRecordId,
                'start_date' => $startDate,
                'end_date' => $endDate,
                'status' => 'active',
                'created_at' => date('Y-m-d H:i:s')
            ];
            
            $subscriptionId = $this->subscriptionModel->createSubscription($subscriptionData);
            
            if (!$subscriptionId) {
                return ['success' => false, 'message' => 'Error al activar la suscripción'];
            }
            
            // Redirigir a página de éxito
            header('Location: /AnimPlanetaS/payment/success?subscription=' . $subscriptionId);
            exit;
        } else {
            // Redirigir a página de error o pendiente según el estado
            header('Location: /AnimPlanetaS/payment/status?payment=' . $paymentRecordId);
            exit;
        }
    }
    
    /**
     * Mostrar página de pago exitoso
     */
    public function showSuccess() {
        // Verificar autenticación
        $auth = new Auth();
        if (!$auth->isAuthenticated()) {
            header('Location: /AnimPlanetaS/login');
            exit;
        }
        
        // Obtener datos de la suscripción
        $subscriptionId = isset($_GET['subscription']) ? (int)$_GET['subscription'] : null;
        
        if (!$subscriptionId) {
            header('Location: /AnimPlanetaS/cuentas/suscripcion');
            exit;
        }
        
        $subscription = $this->subscriptionModel->getSubscriptionById($subscriptionId);
        
        if (!$subscription || $subscription['user_id'] != $_SESSION['user_id']) {
            header('Location: /AnimPlanetaS/cuentas/suscripcion');
            exit;
        }
        
        $plan = $this->subscriptionModel->getPlanById($subscription['plan_id']);
        
        include VIEWS_PATH . '/payment/success.php';
    }
    
    /**
     * Mostrar página de cancelación de pago
     */
    public function showCancel() {
        // Verificar autenticación
        $auth = new Auth();
        if (!$auth->isAuthenticated()) {
            header('Location: /AnimPlanetaS/login');
            exit;
        }
        
        include VIEWS_PATH . '/payment/cancel.php';
    }
    
    /**
     * Crear preferencia de pago en MercadoPago
     */
    private function createPaymentPreference($user, $plan) {
        // Crear ítem para la preferencia
        $item = new \MercadoPago\Item();
        $item->id = $plan['id'];
        $item->title = $plan['name'];
        $item->description = $plan['description'];
        $item->quantity = 1;
        $item->unit_price = $plan['price'];
        $item->currency_id = 'COP'; // Moneda colombiana
        
        // Crear pagador
        $payer = new \MercadoPago\Payer();
        $payer->name = $user['name'] ?? $user['username'];
        $payer->email = $user['email'];
        $payer->identification = array(
            "type" => "CC", // Cédula de ciudadanía
            "number" => $user['identification'] ?? ''
        );
        
        // Crear preferencia
        $preference = new \MercadoPago\Preference();
        $preference->items = array($item);
        $preference->payer = $payer;
        
        // URLs de retorno
        $preference->back_urls = array(
            "success" => SITE_URL . "/payment/success",
            "failure" => SITE_URL . "/payment/cancel",
            "pending" => SITE_URL . "/payment/status"
        );
        
        $preference->auto_return = "approved";
        $preference->binary_mode = true; // Solo aprobado o rechazado, no pendiente
        
        // Metadata para identificar el plan
        $preference->metadata = array(
            "user_id" => $user['id'],
            "plan_id" => $plan['id']
        );
        
        // Guardar preferencia
        $preference->save();
        
        return $preference;
    }
    
    /**
     * Obtener información de un pago desde MercadoPago
     */
    private function getPaymentInfo($paymentId) {
        try {
            $payment = \MercadoPago\Payment::find_by_id($paymentId);
            return $payment;
        } catch (Exception $e) {
            error_log('Error al obtener información de pago: ' . $e->getMessage());
            return null;
        }
    }
    
    /**
     * Calcular fecha de fin de suscripción
     */
    private function calculateSubscriptionEndDate($startDate, $durationMonths) {
        $date = new DateTime($startDate);
        $date->modify("+{$durationMonths} months");
        return $date->format('Y-m-d H:i:s');
    }
    
    /**
     * Verificar si un usuario tiene suscripción activa
     */
    public function hasActiveSubscription($userId) {
        $subscription = $this->subscriptionModel->getActiveSubscriptionByUserId($userId);
        return !empty($subscription);
    }
    
    /**
     * Cancelar suscripción
     */
    public function cancelSubscription($subscriptionId) {
        // Verificar permisos
        $subscription = $this->subscriptionModel->getSubscriptionById($subscriptionId);
        
        if (!$subscription) {
            return ['success' => false, 'message' => 'Suscripción no encontrada'];
        }
        
        if ($subscription['user_id'] != $_SESSION['user_id']) {
            $auth = new Auth();
            if (!$auth->hasRole('admin')) {
                return ['success' => false, 'message' => 'No tiene permisos para cancelar esta suscripción'];
            }
        }
        
        // Actualizar estado de la suscripción
        $result = $this->subscriptionModel->updateSubscriptionStatus($subscriptionId, 'cancelled');
        
        if (!$result) {
            return ['success' => false, 'message' => 'Error al cancelar la suscripción'];
        }
        
        return ['success' => true, 'message' => 'Suscripción cancelada correctamente'];
    }
}